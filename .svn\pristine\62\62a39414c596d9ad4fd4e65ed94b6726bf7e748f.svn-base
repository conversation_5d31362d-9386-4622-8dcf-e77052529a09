
var actionZindex = 1000;
var lyc_tanpai_count = 1;
var lyc_tanpai_time = 1;

function setTuoGuanCountDown_LYC(msg, parentNode, uiOff) {
    if (msg.uid && msg.uid != SelfUid()) {
        var sData = MjClient.data.sData;
        var tData = sData.tData;
        var uids = tData.uids;
        var curIndex = uids.indexOf(msg.uid);
        var selfIndex = uids.indexOf(SelfUid());
        var playerCount = MjClient.MaxPlayerNum;
        var curOff = (curIndex - selfIndex + playerCount) % playerCount;
        var curNode = getNodeLYC(curOff);
        var panelCards = curNode.getChildByName("panel_Cards");
        var curPos = panelCards.convertToWorldSpace(panelCards.getPosition());
        curPos = parentNode.getParent().convertToNodeSpace(curPos);
        parentNode.setPosition(curPos);
    } else {
        parentNode.setPosition(MjClient.size.width/2, MjClient.size.height/2);
    }
    

    var countDownNode = parentNode.getChildByName("number");
    countDownNode.unscheduleAllCallbacks();
    var player = getUIPlayer(uiOff);
    //cc.log("托管倒计时托管倒计时", JSON.stringify(msg))
    if (player && player.info.uid == SelfUid()) {
        countDownNode.visible = true;

        //cc.log("托管倒计时托管倒计时++", JSON.stringify(msg))
        var tipCountDown = msg.tipCountDown;
        var countDownText = countDownNode.getChildByName("TG_CountDown");
        if (!countDownText)
            countDownText = countDownNode;

        countDownText.setString(tipCountDown);
        countDownNode.schedule(function () {
            if (tipCountDown > 0)
                tipCountDown--;

            if (tipCountDown <= 0) {
                countDownNode.setVisible(false);
                parentNode.setVisible(false);
                countDownNode.unscheduleAllCallbacks();
            } else
                countDownText.setString(tipCountDown);

        }, 1, cc.REPEAT_FOREVER, 0);
    }
}

var PlayLayer_laoyancai = cc.Layer.extend({
    jsBind: {
        _event: {
            mjhand: function () {
                if ((MjClient.endoneui != null)) {
                    MjClient.endoneui.removeFromParent(true);
                    MjClient.endoneui = null;
                }
            }, 
            LeaveGame: function () {
                MjClient.addHomeView();
                MjClient.playui.removeFromParent(true);
                delete MjClient.playui;
                delete MjClient.endoneui;
                delete MjClient.endallui;
                cc.audioEngine.stopAllEffects();
                playMusic("bgMain");

            }, 
            endRoom: function (msg) {
                if (msg.showEnd) {
                    MjClient.showToast(msg.des);
                    var seqAction = [];
                    seqAction.push(cc.delayTime(5));
                    seqAction.push(cc.callFunc(function () {
                        MjClient.playui.addChild(new GameOverLayer(), 500);

                    }));
                    this.runAction(cc.sequence(seqAction));
                } else {
                    // MjClient.Scene.addChild(new StopRoomView());
                    MjClient.leaveGame();
                    // 暂时解决解散房间后，亲友圈此玩法桌子卡住问题
                    if (MjClient.FriendCard_main_ui) {
                        MjClient.FriendCard_main_ui.requestClubList(0);
                    }
                }

            }, 
            roundEnd: function (msg) {
                var delayTime = msg.tanPaiCount ? msg.tanPaiCount * lyc_tanpai_time : 2;
                var seqAction = [];
                seqAction.push(cc.delayTime(delayTime));
                var sData = MjClient.data.sData;
                if (!(MjClient.isDismiss)) {
                    var createCbShowCoins = function (startOff, endOff) {
                        return function () {
                            showCoins_LYC(startOff, endOff);
                        };

                    };
                    var createCbShowScores = function (startOff, endOff) {
                        return function () {
                            showScores_LYC(startOff, endOff);
                        };
                    };

                    var pl = getUIPlayer(0);
                    var selfIndex = sData.tData.uids.indexOf(pl.info.uid);
                    var zhuangOff = (((sData.tData.zhuang + MjClient.MaxPlayerNum) - selfIndex) % MjClient.MaxPlayerNum);
                    for (var i = 0; i < MjClient.MaxPlayerNum; i++) {
                        // var selfIndex = sData.tData.uids.indexOf(SelfUid());
                        // var zhuangOff = (((sData.tData.zhuang + MjClient.MaxPlayerNum) - selfIndex) % MjClient.MaxPlayerNum);
                        // if ((sData.tData.areaSelectMode.zhuangType == 1)) {
                        //     zhuangOff = ((MjClient.MaxPlayerNum - selfIndex) % MjClient.MaxPlayerNum);
                        // }
                        if ((sData.tData.zhuang != i)) {
                            var iOff = (((i + MjClient.MaxPlayerNum) - selfIndex) % MjClient.MaxPlayerNum);
                            var iPl = getUIPlayer(iOff);
                            var isCenterJoin = true;
                            for (var id in sData.tData.rungingUids) {
                                if (iPl && (sData.tData.rungingUids[id] == iPl.info.uid)) {
                                    isCenterJoin = false;
                                }
                            }
                            if (iPl && (iPl.winone < 0) && !(isCenterJoin)) {
                                seqAction.push(cc.callFunc(createCbShowCoins(iOff, zhuangOff)));
                                seqAction.push(cc.callFunc(createCbShowScores(iOff, iPl.winone)));
                            }
                        }
                    }
                    for (var i = 0; i < MjClient.MaxPlayerNum; i++) {
                        // var selfIndex = sData.tData.uids.indexOf(SelfUid());
                        // var zhuangOff = (sData.tData.zhuang + MjClient.MaxPlayerNum - selfIndex) % MjClient.MaxPlayerNum;
                        // if (sData.tData.areaSelectMode.zhuangType == 1) {
                        //     zhuangOff = (MjClient.MaxPlayerNum - selfIndex) % MjClient.MaxPlayerNum;
                        // }
                        var iOff = (i + MjClient.MaxPlayerNum - selfIndex) % MjClient.MaxPlayerNum;
                        var iPl = getUIPlayer(iOff);
                        var isCenterJoin = true;
                        for (var id in sData.tData.rungingUids) {
                            if (iPl && sData.tData.rungingUids[id] == iPl.info.uid) {
                                isCenterJoin = false;
                            }
                        }

                        if (!isCenterJoin) {
                            if (sData.tData.zhuang != i) {
                                if (iPl && iPl.winone > 0) {
                                    seqAction.push(cc.callFunc(createCbShowCoins(zhuangOff, iOff)));
                                    seqAction.push(cc.callFunc(createCbShowScores(iOff, iPl.winone)));
                                }
                            }
                            else {
                                if (iPl) {
                                    seqAction.push(cc.callFunc(createCbShowScores(iOff, iPl.winone)));
                                }
                            }
                        }
                    }
                }

                if ((sData.tData.roundNum <= 0)) {
                    seqAction.push(cc.delayTime(5));
                }

                seqAction.push(cc.callFunc(function () {
                    if (MjClient.rePlayVideo != -1 && MjClient.replayui) {
                        //MjClient.replayui.replayEnd();
                    } else {
                        //postEvent("clearCardUI");
                        if (sData.tData.roundNum <= 0) {
                            MjClient.playui.addChild(new GameOverLayer(), 500);
                        } else {
                            ///MJPassConfirmToServer_LYC();
                        }
                    }
                }));

                this.runAction(cc.sequence(seqAction));

            }, 
            initSceneData: function () {
                // cc.log("initSceneData  =================== PlayLayer_laoyancai")
                var tData = MjClient.data.sData.tData;
                if ((tData.tState != TableState.roundFinish)) {
                    resetJiaZhuNum_LYC(this);
                }
                CheckRoomUiDelete();

            }, 
            onlinePlayer: function () {
                var tData = MjClient.data.sData.tData;
                if ((tData.tState != TableState.roundFinish)) {
                    // resetJiaZhuNum_LYC(this);
                }
            }, 
            clearClientUI: function () {
                postEvent("clearCardUI");

            }, 
            logout: function () {
                if (MjClient.playui) {
                    MjClient.addHomeView();
                    MjClient.playui.removeFromParent(true);
                    delete MjClient.playui;
                    delete MjClient.endoneui;
                    delete MjClient.endallui;
                }
            }, 
            DelRoom: function () {
                CheckRoomUiDelete();

            }, 
            changeMJBgEvent: function () {
                changeMJBg(this, getCurrentMJBgType());

            }, 
            NNGetCard: function (data) {
            },
            pkZhaPai: function (msg) {
                if (msg.uid) {
                    playEffectInPlay("zhakai");
                    MjClient.playui.PlayZhaPaiAnimation(msg.uid);
                }
            },
            pkMaBao: function(msg) {
                if (msg.uid != SelfUid()) {
                    playEffectInPlay("mabao");
                } else {
                    // 如果是自己码宝且被限制了，显示提示
                    if (msg.isLimited) {
                        var tData = MjClient.data.sData.tData;
                        MjClient.showMsg("码宝金额已限制为" + tData.areaSelectMode.maxzhu + "分");
                    }
                }
            },
            playTurnBankAnnimation: function(msg) {
                var tData = MjClient.data.sData.tData;
                tData.zhuang = msg.zhuang;
                if (msg.turnUids.length > 1) {
                    MjClient.playui.playTurnBankAni(msg, function() {
                        showUserZhuangLogo_LYC(msg.zhuang, msg.fanbei);
                    });
                    playEffectInPlay("callBank");
                } else {
                    showUserZhuangLogo_LYC(msg.zhuang, msg.fanbei);
                    playEffectInPlay("ding");
                }
            },
            NNSendFourCard: function() {
                SendNewCard_LYC();
                lyc_tanpai_count = 1;
            }
        }, 
        back: {
            back: {
                _run: function () {
                    changeGameBg(this);

                }, _event: {
                    changeGameBgEvent: function () {
                        changeGameBg(this);
                    }
                }, _layout: [[1, 1], [0.5, 1], [0, 0], true]
            }
        }, 
        Image_fapai: {
            _layout: [[0.065, 0.065], [0.5, 0.5], [0, 0], true], 
            _run: function () { this.visible = false; }
        }, 
        Image_banker: {
            _layout: [[0.055, 0.09375], [0.5, 0.5], [0, 0], true], 
            _run: function () { this.visible = false; }
        }, 
        gameName: {
            _layout: [[0.16, 0.16], [0.5, 0.37], [0, 0]],
        }, 
        tableid: {
            _layout: [[0.1, 0.1], [0.01, 0.97], [0, 0]], 
            _run: function () {
                this.ignoreContentAdaptWithSize(true);
            }, 
            _event: {
                initSceneData: function () {
                    this.ignoreContentAdaptWithSize(true);
                    this.setString(("房号:" + MjClient.data.sData.tData.tableid));
                }
            }
        }, 
        jvshu: {
            _layout: [[0.08, 0.1], [0.01, 0.93], [0, 0]], 
            _run: function () {
                this.ignoreContentAdaptWithSize(true);
            }, 
            _event: {
                initSceneData: function () {
                    this.ignoreContentAdaptWithSize(true);
                    var sData = MjClient.data.sData;
                    var tData = sData.tData;
                    this.setString(((("局数:" + ((tData.roundAll - tData.roundNum) + 1)) + "/") + tData.roundAll));
                }, 
                mjhand: function () {
                    this.ignoreContentAdaptWithSize(true);
                    var sData = MjClient.data.sData;
                    var tData = sData.tData;
                    this.setString(((("局数:" + ((tData.roundAll - tData.roundNum) + 1)) + "/") + tData.roundAll));
                }, 
                clearCardUI: function () {
                    this.ignoreContentAdaptWithSize(true);
                    var sData = MjClient.data.sData;
                    var tData = sData.tData;
                    this.setString(((("局数:" + ((tData.roundAll - tData.roundNum) + 1)) + "/") + tData.roundAll));
                }
            }
        }, 
        difen: {
            _layout: [[0.05, 0.1], [0.01, 0.89], [0, 0]], 
            _run: function () {
                this.ignoreContentAdaptWithSize(true);
            }, 
            _event: {
                initSceneData: function () {
                    var tData = MjClient.data.sData.tData;
                    this.ignoreContentAdaptWithSize(true);
                    this.setString(("底分:" + tData.areaSelectMode.diZhu));
                }
            }
        },
        roominfo: {
            _layout: [[0.15, 0.12], [0.01, 0.87], [0, 0]],
            _run: function () {
                this.ignoreContentAdaptWithSize(true);
            },
            _event: {
                initSceneData: function () {
                    this.ignoreContentAdaptWithSize(true);
                    var tData = MjClient.data.sData.tData;
                    var infoText = "";

                    // 显示码宝限制信息
                    if (tData.areaSelectMode.maxzhu > 0) {
                        infoText = "码宝上限:" + tData.areaSelectMode.maxzhu + "分";
                        this.setString(infoText);
                    }
                }
            }
        },
        minCallBank: {
            _layout: [[0.14, 0.12], [0.01, 0.76], [0, 0]], 
            _run: function () {
                this.ignoreContentAdaptWithSize(true);
            },
            _event: {
                initSceneData: function () {
                    var tData = MjClient.data.sData.tData;
                    this.ignoreContentAdaptWithSize(true);
                    if (tData.areaSelectMode.minCallBankLimit) {
                        var desc = "低于" + tData.areaSelectMode.minCallBankLimit + "分不能抢庄"
                        this.setString(desc);
                    }
                }
            }
        },
        btn_setting: {
            _layout: [[0.095, 0.095], [0.95, 0.93], [0, 0]], 
            _click: function (btn, et) {
                var settringLayer = new SettingView();
                settringLayer.setName("PlayLayerClick");
                MjClient.Scene.addChild(settringLayer);
            }
        }, 
        btn_lookon: {
            _layout: [[0.095, 0.095], [0.87, 0.93], [0, 0]],
            _click: function (btn, et) {
                var lookOnLayer = new LookOnLayer();
                MjClient.Scene.addChild(lookOnLayer);
            }
        },
        btn_yaoqing: {
            _layout: [[0.05, 0.05], [0.99, 0.5], [0, 0]],
            _click: function () {
                var tData = MjClient.data.sData.tData;
                var clubInfoTable = getClubInfoInTable();
                if (clubInfoTable) {
                    MjClient.Scene.addChild(new FriendCard_yaoqingMember(clubInfoTable.clubId, tData.tableid));
                }
            }
        },
        jiazhuWait: {
            _visible: false, 
            _layout: [[0.4, 0.4], [0.5, 0.42], [0, 0]], 
            _event: {
                moveHead: function () { this.visible = false; }
            }
        }, 
        eat: {
            optime_bg: {
                _run: function () {
                    setWgtLayout(this, [0.12, 0.12], [0.5, 0.5], [0, 0]);
                    var ccNumber = this.getChildByName("number");
                    ccNumber.setString("0");
                    ccNumber.ignoreContentAdaptWithSize(true);
                    this.visible = false;
                }, 
                _event: {
                    trustTip: function (msg) {
                        this.visible = true;
                        setTuoGuanCountDown_LYC(msg, this, 0);
                    }, 
                    roundEnd: function () { this.visible = false; }
                }
            }, 
            _event: {
                waitJiazhu: function (msg) {
                    if ((MjClient.rePlayVideo !== -1)) {
                        return;
                    }
                }
            }
        }, 
        wait: {
            BtnReady: {
                _visible: false, 
                _run: function () {
                    setWgtLayout(this, [0.13,0.11], [0.5, 0.34], [0, 0]);
                }, 
                _click: function () {
                    this.visible = false;
                    if ((MjClient.rePlayVideo != -1)) {
                        return;
                    }
                    MjClient.gamenet.request("pkroom.handler.tableMsg", { cmd: "MJPass" });
                }, 
                _event: {
                    waitReady: function () {
                        if (isLookOn(SelfUid())) {
                            this.visible = false;
                        } else {
                            this.visible = true;
                        }
                    },  
                    mjhand: function () {
                        this.visible = false;
                    }, 
                    initSceneData: function () {
                        this.visible = false;
                        var sData = MjClient.data.sData;
                        var tData = sData.tData;
                        // if ((tData.roundNum != tData.roundAll)) {
                        //     return;
                        // }
                        var pl = sData.players[SelfUid()];
                        if (pl && (tData.tState == TableState.waitReady) && (pl.mjState == TableState.waitReady)) {
                            this.visible = true;
                        }
                    }, 
                    PKPass: function () {
                        this.visible = false;
                    }, 
                    removePlayer: function (eD) {
                        this.visible = false;
                    },
                    startCallBank: function () {
                        this.visible = false;
                    },
                    waitJiazhu: function () {
                        this.visible = false;
                    },
                    NNSendFourCard: function () {
                        this.visible = false;
                    },
                    onlinePlayer: function (msg) {
                        if ((msg.uid == SelfUid())) {
                            this.visible = false;
                        }
                    }
                }
            }
        },
        Node_player1: {
            _layout: [[0.2, 0.315], [0.5, 0.16], [0, 0]],
            head: {
                AtlasLabel_Score: {
                    _run: function () {
                        this.visible = false;
                    }
                }, 
                kuang: {
                    _run: function () {
                        this.visible = false;
                    },
                    _event: {
                        initSceneData: function (msg) {
                            var tData = MjClient.data.sData.tData;
                            var pl = getUIPlayer(0);
                            if (pl && (tData.uids[tData.zhuang] == pl.info.uid)) {
                                this.visible = true;
                            } else {
                                this.visible = false;
                            }
                        },
                        clearCardUI: function () {
                            this.visible = false;
                        }
                    }
                }, 
                zhuang: {
                    _run: function () {
                        this.visible = false;
                    },
                    _event: {
                        initSceneData: function (msg) {
                            var tData = MjClient.data.sData.tData;
                            var pl = getUIPlayer(0);
                            if (pl && (tData.uids[tData.zhuang] == pl.info.uid)) {
                                this.visible = true;
                            } else {
                                this.visible = false;
                            }
                        },
                        clearCardUI: function () {
                            this.visible = false;
                        }
                    }
                }, 
                tuoguan: {
                    _run: function () {
                        this.visible = false;
    
                    }, _event: {
                        beTrust: function (msg) {
                            if (getUIPlayer(0) && (getUIPlayer(0).info.uid == msg.uid)) {
                                this.visible = true;
                            }
    
                        }, cancelTrust: function (msg) {
                            if (getUIPlayer(0) && (getUIPlayer(0).info.uid == msg.uid)) {
                                this.visible = false;
                            }
    
                        }, initSceneData: function (msg) {
                            var pl = getUIPlayer(0);
                            if (pl && pl.trust) {
                                this.visible = true;
                            } else {
                                this.visible = false;
                            }
                        }
                    }
                }, 
                chatbg: {
                    _run: function () {
                        this.getParent().zIndex = 600;
    
                    }, chattext: {
                        _event: {
                            MJChat: function (msg) {
                                showUserChat(this, 0, msg);
                            }, playVoice: function (voicePath) {
                                MjClient.data._tempMessage.msg = voicePath;
                                showUserChat(this, 0, MjClient.data._tempMessage);
                            }
                        }
                    }
                }, 
                jiaZhu: {
                    _run: function () {
                        this.visible = false;
                        this.setString("0");
                        this.ignoreContentAdaptWithSize(true);
    
                    }, 
                    _event: {
                        MJJiazhu: function (msg) {
                            var pl = getUIPlayer(0);
                            if (pl && (msg.uid == pl.info.uid)) {
                                this.visible = true;
                                this.setString(msg.jiazhuNum);
                            }
                        }, 
                        roundEnd: function (eD) {
                            this.visible = false;
                            this.setString("0");
                        }
                    }
                }, 
                gold_icon: {
                    _run: function () {
                        this.visible = false;
                    }, 
                    _event: {
                        roundEnd: function (eD) {
                            this.visible = false;
                        }, 
                        MJJiazhu: function (msg) {
                            var pl = getUIPlayer(0);
                            if (pl && (msg.uid == pl.info.uid)) {
                                this.visible = true;
                            }
                        }
                    }
                }, 
                _click: function (btn) {
                    showPlayerInfo(0, btn);
                }, 
                _run: function () { }, 
                _event: {
                    loadWxHead: function (d) {
                        setWxHead(this, d, 0);
                    },
                    addPlayer: function (eD) { },
                    removePlayer: function (eD) { }
                }
            }, 
            panel_QiangZhuang1: {
                BtnQiangZhuang1: {
                    _click: function (btn) {
                        if ((MjClient.rePlayVideo !== -1)) {
                            return;
                        }
                        var sendMsg = { cmd: "NNCallBank", callBankNum: 1 };
                        MjClient.gamenet.request("pkroom.handler.tableMsg", sendMsg);
                        MjClient.playui.panel_QiangZhuang1.visible = false;
    
                    }
                }, 
                BtnQiangZhuang2: {
                    _click: function (btn) {
                        if ((MjClient.rePlayVideo !== -1)) {
                            return;
                        }
                        var sendMsg = { cmd: "NNCallBank", callBankNum: 2 };
                        MjClient.gamenet.request("pkroom.handler.tableMsg", sendMsg);
                        MjClient.playui.panel_QiangZhuang1.visible = false;
    
                    }
                }, 
                BtnQiangZhuang3: {
                    _click: function (btn) {
                        if ((MjClient.rePlayVideo !== -1)) {
                            return;
                        }
                        var sendMsg = { cmd: "NNCallBank", callBankNum: 3 };
                        MjClient.gamenet.request("pkroom.handler.tableMsg", sendMsg);
                        MjClient.playui.panel_QiangZhuang1.visible = false;
    
                    }
                }, 
                BtnQiangZhuang4: {
                    _click: function (btn) {
                        if ((MjClient.rePlayVideo !== -1)) {
                            return;
                        }
                        var sendMsg = { cmd: "NNCallBank", callBankNum: 4 };
                        MjClient.gamenet.request("pkroom.handler.tableMsg", sendMsg);
                        MjClient.playui.panel_QiangZhuang1.visible = false;
    
                    }
                }, 
                BtnBuQiang: {
                    _click: function (btn) {
                        if ((MjClient.rePlayVideo !== -1)) {
                            return;
                        }
                        var sendMsg = { cmd: "NNCallBank", callBankNum: 0 };
                        MjClient.gamenet.request("pkroom.handler.tableMsg", sendMsg);
                        MjClient.playui.panel_QiangZhuang1.visible = false;
                    }
                }, 
                _event: {
                    initSceneData: function (eD) {
                        cc.log("initSceneData === panel_QiangZhuang1")
                        if (isLookOn(SelfUid())) {
                            this.visible = false;
                            return;
                        }
                        var pl = getUIPlayer(0);
                        if (pl && (pl.mjState == TableState.waitCallBank) && (MjClient.rePlayVideo == -1)) {
                            var tData = MjClient.data.sData.tData;
                            this.visible = (tData.areaSelectMode.zhuangType == 0);
                        } else {
                            this.visible = false;
                        }
                    }, startCallBank: function (d) {
                        if (isLookOn(SelfUid())) {
                            this.visible = false;
                            return;
                        }
                        if (isLimitScore(0)) {
                            var tData = MjClient.data.sData.tData;
                            this.visible = (tData.areaSelectMode.zhuangType == 0);
                        }
                    }, clearCardUI: function () {
                        this.visible = false;
                    }, NNCallBank: function (d) {
                        if ((d.uid == SelfUid())) {
                            this.visible = false;
                            if ((d.errReason == 1)) {
                                MjClient.showMsg("分数太低，不能抢庄哦！");
                            }
                        }
                        // playEffectInPlay(("qiangzhuang" + d.callBankNum));
                        playEffectInPlay("beiShu");
                        
                    }, waitJiazhu: function (msg) {
                        this.visible = false;
                    }
                }
            }, 
            panel_BetScore1: {
                btn_bet_0: {
                    _click: function (btn) {
                        if ((MjClient.rePlayVideo !== -1)) {
                            return;
                        }
                        var zhuNum = MjClient.playui.getBetScore(0);
                        MjClient.gamenet.request("pkroom.handler.tableMsg", { cmd: "MJJiazhu", jiazhuNum: parseInt(zhuNum) });
                        MjClient.playui.panel_BetScore1.visible = false;
                    }
                }, 
                btn_bet_1: {
                    _click: function (btn) {
                        if ((MjClient.rePlayVideo !== -1)) {
                            return;
                        }
                        var zhuNum = MjClient.playui.getBetScore(1);
                        MjClient.gamenet.request("pkroom.handler.tableMsg", { cmd: "MJJiazhu", jiazhuNum: parseInt(zhuNum) });
                        MjClient.playui.panel_BetScore1.visible = false;
                    }
                }, 
                btn_bet_2: {
                    _click: function (btn) {
                        if ((MjClient.rePlayVideo !== -1)) {
                            return;
                        }
                        var zhuNum = MjClient.playui.getBetScore(2);
                        MjClient.gamenet.request("pkroom.handler.tableMsg", { cmd: "MJJiazhu", jiazhuNum: parseInt(zhuNum) });
                        MjClient.playui.panel_BetScore1.visible = false;
                    }
                }, 
                btn_bet_3: {
                    _click: function (btn) {
                        if ((MjClient.rePlayVideo !== -1)) {
                            return;
                        }
                        var zhuNum = MjClient.playui.getBetScore(3);
                        MjClient.gamenet.request("pkroom.handler.tableMsg", { cmd: "MJJiazhu", jiazhuNum: parseInt(zhuNum) });
                        MjClient.playui.panel_BetScore1.visible = false;
                    }
                }, 
                btn_mabao: {
                    _visible: false,
                    _click: function (btn) {
                        if ((MjClient.rePlayVideo !== -1)) {
                            return;
                        }

                        //加注上一局赢分（服务器端会处理限制）
                        MjClient.gamenet.request("pkroom.handler.tableMsg", { cmd: "pkMaBao"});
                        MjClient.playui.panel_BetScore1.visible = false;
                        playEffectInPlay("mabao");
                    },
                    _event: {
                        waitJiazhu: function (msg) {
                            var pl = getUIPlayer(0);
                            if (pl && msg.mabaoUids && msg.mabaoUids.indexOf(pl.info.uid) > -1) {
                                this.visible = true;
                            } else {
                                this.visible = false;
                            }
                        },
                        initSceneData: function (eD) {
                            var pl = getUIPlayer(0);
                            if (pl && pl.lastWinOne > 0) {
                                this.visible = true;
                            } else {
                                this.visible = false;
                            }
                        }
                    }
                }, 
                _event: {
                    initSceneData: function (eD) {
                        // cc.log(("+++++++++++++ panel_BetScore1 initSceneData = " + JSON.stringify(eD)));
                        if (isLookOn(SelfUid())) {
                            this.visible = false;
                            return;
                        }
                        var pl = getUIPlayer(0);
                        var tData = MjClient.data.sData.tData;
                        if ((isCenterJoin(0) == false) && pl && (pl.mjState == TableState.waitJiazhu) && (MjClient.rePlayVideo == -1) && (tData.uids[tData.zhuang] != SelfUid())) {
                            this.visible = true;
                            for (var i = 0; i < 4; i++) {
                                var btnBet = MjClient.playui._btn_betArr1[i];
                                var nBetScore = MjClient.playui.getBetScore(i);
                                btnBet.getChildByName("Text_num").setString(nBetScore);
                                btnBet.getChildByName("Text_num").ignoreContentAdaptWithSize(true);
                            }
                        } else {
                            this.visible = false;
                        }
                    }, 
                    waitJiazhu: function (msg) {
                        if (isLookOn(SelfUid())) {
                            this.visible = false;
                            return;
                        }
                        var pl = getUIPlayer(0);
                        var tData = MjClient.data.sData.tData;
                        if ((isCenterJoin(0) == false) && pl && (pl.mjState == TableState.waitJiazhu) && (tData.uids[msg.zhuang] != SelfUid())) {
                            this.visible = true;
                            for (var i = 0; i < 4; i++) {
                                var btnBet = MjClient.playui._btn_betArr1[i];
                                var nBetScore = MjClient.playui.getBetScore(i);
                                btnBet.getChildByName("Text_num").setString(nBetScore);
                                btnBet.getChildByName("Text_num").ignoreContentAdaptWithSize(true);
                            }
                        } else {
                            this.visible = false;
                        }
                    }, 
                    clearCardUI: function () {
                        this.visible = false;
                    }, 
                    MJJiazhu: function (d) {
                        if ((d.uid == SelfUid())) {
                            this.visible = false;
                        }
                    }
                }
            }, 
            panel_LaoPai: {
                btn_laopai: {
                    _click: function (btn) {
                        if ((MjClient.rePlayVideo !== -1)) {
                            return;
                        }
                        MjClient.gamenet.request("pkroom.handler.tableMsg", { cmd: "pkLaoPai", laopai: true });
                        MjClient.playui.panel_LaoPai.visible = false;
                    }
                },
                btn_bulao: {
                    _click: function (btn) {
                        if ((MjClient.rePlayVideo !== -1)) {
                            return;
                        }
                        MjClient.gamenet.request("pkroom.handler.tableMsg", { cmd: "pkLaoPai", laopai: false });
                        MjClient.playui.panel_LaoPai.visible = false;
                        playEffectInPlay("bulao");
                    }
                },
                btn_bipai: {
                    _visible: false,
                    _click: function (btn) {
                        if ((MjClient.rePlayVideo !== -1)) {
                            return;
                        }
                        var nBiCount = 0;
                        var biUid = 0;
                        var sData = MjClient.data.sData;
                        var tData = MjClient.data.sData.tData;
                        for (var id in sData.tData.rungingUids) {
                            var uid = tData.rungingUids[id];
                            if (uid != tData.uids[tData.zhuang]) {
                                var pl = sData.players[uid];
                                if (pl && !pl.hasBiPai) {
                                    nBiCount++;
                                    biUid = uid;
                                }
                            }
                        }
                        if (nBiCount > 0) {
                            MjClient.playui.panel_LaoPai.visible = false;
                            if (nBiCount > 1) {
                                postEvent("docompare"); 
                            } else {
                                var sendMsg = {cmd:"pkCompareCard", uid:biUid};
                                MjClient.gamenet.request("pkroom.handler.tableMsg",sendMsg); 
                            }
                        }
                    },
                    _event: {
                        initSceneData: function (eD) {
                            this.visible = false;
                            var pl = getUIPlayer(0);
                            var tData = MjClient.data.sData.tData;
                            if ((isCenterJoin(0) == false) && (MjClient.rePlayVideo == -1)) {
                                if (pl && (tData.uids[tData.zhuang] == pl.info.uid) && (pl.mjState == TableState.waitLaoPai) && tData.tState == TableState.waitLaoPai) {
                                    this.visible = true;
                                }
                            }
                        },
                        nfLaoPai: function(msg) {
                            var tData = MjClient.data.sData.tData;
                            var pl = getUIPlayer(0);
                            if (pl && (tData.uids[tData.zhuang] == pl.info.uid)) {
                                this.visible = true;
                            } else {
                                this.visible = false;
                            }
                        }
                    }
                },
                _event: {
                    initSceneData: function (eD) {
                        if (isLookOn(SelfUid())) {
                            this.visible = false;
                            return;
                        }
                        var pl = getUIPlayer(0);
                        var tData = MjClient.data.sData.tData;
                        if ((isCenterJoin(0) == false) && (MjClient.rePlayVideo == -1)) {
                            if (pl && (pl.mjState == TableState.waitLaoPai) && tData.tState == TableState.waitLaoPai) {
                                this.visible = true;
                            } else {
                                this.visible = false;
                            }
                        } else {
                            this.visible = false;
                        }
                    },
                    nfLaoPai: function() {
                        if (isCenterJoin(0) == false) {
                            this.visible = true;
                        }
                    },
                    buLaoPai: function (msg) {
                        var pl = getUIPlayer(0);
                        if (pl && pl.info.uid == msg.uid) {
                            this.visible = false;
                        }
                        if (msg.uid != SelfUid()) {
                            playEffectInPlay("bulao");
                        }
                    },
                    pkTanPai: function () {
                        this.visible = false;
                    },
                    clearCardUI: function () {
                        this.visible = false;
                    }, 
                }
            },
            panel_ZhaPai: {
                btn_zhapai: {
                    _click: function (btn) {
                        if ((MjClient.rePlayVideo !== -1)) {
                            return;
                        }
                        MjClient.gamenet.request("pkroom.handler.tableMsg", { cmd: "pkZhaPai", zhapai: true });
                        MjClient.playui.panel_ZhaPai.visible = false;
                    }
                },
                btn_buzha: {
                    _click: function (btn) {
                        if ((MjClient.rePlayVideo !== -1)) {
                            return;
                        }
                        MjClient.gamenet.request("pkroom.handler.tableMsg", { cmd: "pkZhaPai", zhapai: false });
                        MjClient.playui.panel_ZhaPai.visible = false;
                    }
                },
                _event: {
                    initSceneData: function (eD) {
                        if (isLookOn(SelfUid())) {
                            this.visible = false;
                            return;
                        }
                        var pl = getUIPlayer(0);
                        var tData = MjClient.data.sData.tData;
                        if ((isCenterJoin(0) == false) && (MjClient.rePlayVideo == -1)) {
                            if (pl && (pl.mjState == TableState.waitZhaPai) && tData.tState == TableState.waitZhaPai) {
                                this.visible = true;
                            } else {
                                this.visible = false;
                            }
                        } else {
                            this.visible = false;
                        }
                    },
                    nfZhaPai: function(msg) {
                        var pl = getUIPlayer(0);
                        if (pl && (pl.info.uid == msg.uid)) {
                            this.visible = true;
                        }
                    },
                    buZhaPai: function () {
                        this.visible = false;
                    },
                    pkTanPai: function () {
                        this.visible = false;
                    },
                    clearCardUI: function () {
                        this.visible = false;
                    }, 
                }
            },
            panel_Cards: {
                _visible: false, 
                _run: function () {
                    this.visible = false;
                }, 
                _event: {
                    NNSendFourCard: function (mjHandMsg) {
                        this.visible = false;
                        var pl = getUIPlayer(0);
                        if (pl && mjHandMsg[pl.info.uid]) {
                            this.visible = true;
                            cc.log("NNSendFourCard === visible", pl.info.uid, JSON.stringify(mjHandMsg))
                        }
                    }, 
                    mjhand: function(mjhandMsg) {
                        var pl = getUIPlayer(0);
                        var sData = MjClient.data.sData;
                        var plyCount = sData.tData.rungingUids.length;
                        if (pl && pl.info.uid == mjhandMsg.uid) {
                            pl.mjhand = mjhandMsg.mjhand;
                            cc.log("mjhand ===", mjhandMsg.uid, JSON.stringify(mjhandMsg.mjhand))
                        }
                    },
                    initSceneData: function (msg) {
                        // cc.log("panel_Cards initSceneData ===", JSON.stringify(msg))
                        this.visible = false;
                        var tData = MjClient.data.sData.tData;
                        var pl = getUIPlayer(0);
                        if (pl && msg.players[pl.info.uid] && (isCenterJoin(0) == false)) {
                            if (tData.tState == TableState.waitCard || tData.tState == TableState.waitZhaPai || tData.tState == TableState.waitLaoPai) {
                                this.visible = true;
                                ShowTwoCard_LYC(this, 0, false);
                            }
                        }
                    }, 
                    pkLaoPai: function (msg) {
                        var pl = getUIPlayer(0);
                        if (pl && msg.uid && (pl.info.uid == msg.uid)) {
                            if (msg.newCard) {
                                playEffectInPlay("laopai");
                                MjClient.playui.playLaoPaiAni();
                                ShowLaoCard_LYC(this, 0, msg.newCard, true);
                                this.visible = true;
                            }
                        } else if (msg.newCard) {
                            playEffectInPlay("laopai");
                            MjClient.playui.playLaoPaiAni();
                        }
                    },
                    pkTanPai: function (msg) {
                        var pl = getUIPlayer(0);
                        if (pl && (pl.info.uid == msg.uid)) {
                            pl.mjhand = msg.mjhand;
                            pl.handScore = msg.handScore;
                            var that = this;
                            this.runAction(cc.sequence(cc.delayTime((lyc_tanpai_time * lyc_tanpai_count++)), 
                                cc.callFunc(function () {
                                    ShowAllCard_LYC(that, 0);
                                })));
                            this.visible = true;
                        }
                    },
                    nfTanPai: function (msg) {
                        var pl = getUIPlayer(0);
                        if (pl && (pl.info.uid == msg.uid)) {
                            pl.mjhand = msg.mjhand;
                            pl.handScore = msg.handScore;
                            pl.hasBiPai = msg.hasBiPai;
                            // ShowAllCard_LYC(this, 0);
                            var that = this;
                            this.runAction(cc.sequence(cc.delayTime(1), 
                                cc.callFunc(function () {
                                    ShowAllCard_LYC(that, 0);
                                })));
                            this.visible = true;
                        }
                    },
                    startCallBank: function () {
                        this.visible = false;
                    }
                }
            }, 
            play_Qiang_NN: {
                _visible: false, _event: {
                    NNCallBank: function (msg) {
                        var pl = getUIPlayer(0);
                        if (pl && (pl.info.uid == msg.uid)) {
                            this.visible = true;
                            this.loadTexture((("poker/niuniu/Sprite_mul" + msg.callBankNum) + ".png"));
                        }
                    }, clearCardUI: function () {
                        this.visible = false;
                    }, mjhand: function (eD) {
                        this.visible = false;
                    }, 
                    waitJiazhu: function (eD) {
                        this.visible = false;
                    }, 
                    // pkTanPai: function (eD) {
                    //     this.visible = false;
                    // }, 
                    initSceneData: function (msg) {
                        var pl = getUIPlayer(0);
                        var sData = MjClient.data.sData;
                        var tData = sData.tData;
                        if ((tData.tState == TableState.waitJiazhu) || (tData.tState == TableState.waitCallBank)) {
                            for (var uid in msg.players) {
                                if (pl && (pl.info.uid == uid)) {
                                    if (([1, 2, 3, 4].indexOf(msg.players[uid].callBankNum) != -1)) {
                                        this.visible = true;
                                        this.loadTexture((("poker/niuniu/Sprite_mul" + msg.players[uid].callBankNum) + ".png"));
                                    } else {
                                        this.visible = false;
                                    }
                                }
                            }
                        } else {
                            this.visible = false;
                        }
                    }, 
                    roundEnd: function () {
                        this.visible = false;
                    }
                }
            }, 
            play_tip_NN: {
                _visible: false, _event: {
                    clearCardUI: function () {
                        this.visible = false;
                    }, startCallBank: function () {
                        this.visible = false;
                    }
                }
            }, 
            play_bi_pai: {
                _visible: false,
                _event: {
                    nfTanPai: function (msg) {
                        var pl = getUIPlayer(0);
                        if (pl && (pl.info.uid == msg.uid)) {
                            if (msg.biPaiResult) {
                                if (msg.biPaiResult > 0){
                                    this.loadTexture("poker/laoyancai/win.png");
                                } else {
                                    this.loadTexture("poker/laoyancai/lose.png");
                                }
                                this.visible = true;
                            }
                        }
                    },
                    clearCardUI: function () {
                        this.visible = false;
                    }
                }
            },
            ready: {
                _run: function () {
                    GetReadyVisible(this, 0);
                }, 
                _event: {
                    moveHead: function () {
                        GetReadyVisible(this, -1);
                    }, addPlayer: function () {
                        GetReadyVisible(this, 0);
                    }, removePlayer: function () {
                        GetReadyVisible(this, 0);
                    }, onlinePlayer: function () {
                        GetReadyVisible(this, 0);
                    }, NNSendFourCard: function (eD) {
                        this.visible = false;
                    }, initSceneData: function (eD) {
                        GetReadyVisible(this, 0);
                    },
                    startCallBank: function() {
                        this.visible = false;
                    }
                }
            },  
            _event: {
                clearCardUI: function () { }, 
                initSceneData: function (eD) {
                    SetUserVisible_LYC(this, 0);
                }, addPlayer: function (eD) {
                    SetUserVisible_LYC(this, 0);
                }, removePlayer: function (eD) {
                    SetUserVisible_LYC(this, 0);
                }, mjhand: function (eD) {
                    InitUserHandUI_LYC(this, 0);
                }, roundEnd: function () {
                    InitUserCoinAndName(this, 0);
                }, 
                onlinePlayer: function (msg) {
                    var pl = getUIPlayer(0);
                    if (pl && (pl.info.uid == msg.uid)) {
                        pl.onLine = msg.onLine;
                        setUserOffline(this, 0);
                    }
                }
            }
        },
        Node_player2: {
            _layout: [[0.2, 0.315], [0.87, 0.4], [0, 0]],
            head: {
                AtlasLabel_Score: {
                    _run: function () { this.visible = false; }
                }, 
                ready: {
                    _run: function () {
                        GetReadyVisible(this, 1);
                    }, _event: {
                        addPlayer: function () {
                            GetReadyVisible(this, 1);
                        }, removePlayer: function () {
                            GetReadyVisible(this, 1);
                        }, NNSendFourCard: function (eD) {
                            this.visible = false;
                        }, onlinePlayer: function () {
                            GetReadyVisible(this, 1);
                        },
                        startCallBank: function() {
                            this.visible = false;
                        }
                    }
                }, 
                kuang: {
                    _run: function () { this.visible = false; }, 
                    _event: {
                        initSceneData: function (msg) {
                            var pl = getUIPlayer(1);
                            var tData = MjClient.data.sData.tData;
                            if (pl && (tData.uids[tData.zhuang] == pl.info.uid)) {
                                this.visible = true;
                            } else {
                                this.visible = false;
                            }
                        },
                        clearCardUI: function () { this.visible = false; }
                    }
                }, 
                zhuang: {
                    _run: function () { this.visible = false; }, 
                    _event: {
                        initSceneData: function (msg) {
                            var pl = getUIPlayer(1);
                            var tData = MjClient.data.sData.tData;
                            if (pl && (tData.uids[tData.zhuang] == pl.info.uid)) {
                                this.visible = true;
                            } else {
                                this.visible = false;
                            }
                        },
                        clearCardUI: function () { this.visible = false; }
                    }
                }, 
                play_tip_NN: {
                    _run: function () { this.zIndex = actionZindex; }, 
                    _visible: false, 
                    _event: {
                        clearCardUI: function () { this.visible = false; }, 
                        initSceneData: function (eD) { }, 
                        startCallBank: function () { this.visible = false; }
                    }
                }, 
                play_Qiang_NN: {
                    _visible: false, _run: function () {
                        this.zIndex = 500;
                    }, 
                    _event: {
                        NNCallBank: function (msg) {
                            var pl = getUIPlayer(1);
                            if (pl && (pl.info.uid == msg.uid)) {
                                this.visible = true;
                                this.loadTexture((("poker/niuniu/Sprite_mul" + msg.callBankNum) + ".png"));
                            }
                        }, 
                        clearCardUI: function () {
                            this.visible = false;
                        },
                        // pkTanPai: function (eD) {
                        //     this.visible = false;
                        // }, 
                        mjhand: function (eD) {
                            this.visible = false;
                        }, 
                        waitJiazhu: function (eD) {
                            this.visible = false;
                        }, 
                        initSceneData: function (msg) {
                            var pl = getUIPlayer(1);
                            var sData = MjClient.data.sData;
                            var tData = sData.tData;
                            if ((tData.tState == TableState.waitJiazhu) || (tData.tState == TableState.waitCallBank)) {
                                for (var uid in msg.players) {
                                    if (pl && (pl.info.uid == uid)) {
                                        this.visible = true;
                                        this.loadTexture((("poker/niuniu/Sprite_mul" + msg.players[uid].callBankNum) + ".png"));
                                    }
                                }
                            } else {
                                this.visible = false;
                            }
                        }, 
                        roundEnd: function () {
                            this.visible = false;
                        }
                    }
                }, 
                tuoguan: {
                    _run: function () {
                        this.visible = false;
                    }, _event: {
                        beTrust: function (msg) {
                            if (getUIPlayer(1) && (getUIPlayer(1).info.uid == msg.uid)) {
                                this.visible = true;
                            }
                        }, 
                        cancelTrust: function (msg) {
                            if (getUIPlayer(1) && (getUIPlayer(1).info.uid == msg.uid)) {
                                this.visible = false;
                            }
                        }, 
                        initSceneData: function (msg) {
                            var pl = getUIPlayer(1);
                            if (pl && pl.trust) {
                                this.visible = true;
                            } else {
                                this.visible = false;
                            }
                        }
                    }
                }, 
                chatbg: {
                    _run: function () {
                        this.getParent().zIndex = 500;
                    }, 
                    chattext: {
                        _event: {
                            MJChat: function (msg) {
                                showUserChat(this, 1, msg);
                            }, 
                            playVoice: function (voicePath) {
                                MjClient.data._tempMessage.msg = voicePath;
                                showUserChat(this, 1, MjClient.data._tempMessage);
                            }
                        }
                    }
                }, 
                jiaZhu: {
                    _run: function () {
                        this.visible = false;
                        this.setString("0");
                        this.ignoreContentAdaptWithSize(true);
                    }, 
                    _event: {
                        MJJiazhu: function (msg) {
                            var pl = getUIPlayer(1);
                            if (pl && (msg.uid == pl.info.uid)) {
                                this.visible = true;
                                this.setString(msg.jiazhuNum);
                            }
                        }, 
                        roundEnd: function (eD) {
                            this.visible = false;
                            this.setString("0");
                        }
                    }
                }, 
                gold_icon: {
                    _run: function () {
                        this.visible = false;
                    }, 
                    _event: {
                        roundEnd: function (eD) {
                            this.visible = false;
                        }, 
                        MJJiazhu: function (msg) {
                            var pl = getUIPlayer(1);
                            if (pl && (msg.uid == pl.info.uid)) {
                                this.visible = true;
                            }
                        }
                    }
                },
                play_bi_pai: {
                    _visible: false,
                    _event: {
                        nfTanPai: function (msg) {
                            var pl = getUIPlayer(1);
                            if (pl && (pl.info.uid == msg.uid)) {
                                if (msg.biPaiResult) {
                                    if (msg.biPaiResult > 0){
                                        this.loadTexture("poker/laoyancai/win.png");
                                    } else {
                                        this.loadTexture("poker/laoyancai/lose.png");
                                    }
                                    this.visible = true;
                                }
                            }
                        },
                        clearCardUI: function () {
                            this.visible = false;
                        }
                    }
                },
                compare:{
                    _run:function () { this.visible=false; },
                    _click:function () { 
                        postEvent("endcompare");
                        if ((MjClient.rePlayVideo !== -1)) {
                            return;
                        }
                        var pl = getUIPlayer(1);
                        if (!pl) {
                            return;
                        }
                        var sendMsg = {cmd:"pkCompareCard", uid:pl.info.uid};
                        MjClient.gamenet.request("pkroom.handler.tableMsg",sendMsg); 
                    },
                    _event:{
                        clearCardUI:function () { this.visible=false; },
                        docompare:function () { 
                            var pl = getUIPlayer(1);
                            if (pl && !pl.hasBiPai && !isCenterJoin(1)) {
                                this.visible=true;
                            } 
                        },
                        endcompare:function () { this.visible=false; }
                    }
                },
                _click: function (btn) {
                    showPlayerInfo(1, btn);
                }, 
                _run: function () { },
                _event: {
                    loadWxHead: function (d) {
                        setWxHead(this, d, 1);
                    }, 
                    addPlayer: function (eD) { }, 
                    removePlayer: function (eD) { }
                }
            }, 
            panel_Cards: {
                _run: function () {
                    this.visible = false;
                }, 
                _event: {
                    NNSendFourCard: function (mjHandMsg) {
                        var pl = getUIPlayer(1);
                        this.visible = false;
                        if (pl && mjHandMsg[pl.info.uid]) {
                            this.visible = true;
                        }
                    }, 
                    initSceneData: function (msg) {
                        this.visible = false;
                        var pl = getUIPlayer(1);
                        if (pl && msg.players[pl.info.uid] && (isCenterJoin(1) == false)) {
                            var tData = MjClient.data.sData.tData;
                            if (tData.tState == TableState.waitCard || tData.tState == TableState.waitZhaPai || tData.tState == TableState.waitLaoPai) {
                                this.visible = true;
                                ShowTwoCard_LYC(this, 1, false);
                            }
                        }
                    }, 
                    pkLaoPai: function (msg) {
                        var pl = getUIPlayer(1);
                        if (pl && msg.uid && (pl.info.uid == msg.uid) && msg.newCard) {
                            ShowLaoCard_LYC(this, 0, msg.newCard, true);
                            this.visible = true;
                        }
                    },
                    pkTanPai: function (msg) {
                        var pl = getUIPlayer(1);
                        if (pl && (pl.info.uid == msg.uid)) {
                            pl.mjhand = msg.mjhand;
                            pl.handScore = msg.handScore;
                            var that = this;
                            this.runAction(cc.sequence(cc.delayTime((lyc_tanpai_time * lyc_tanpai_count++)), 
                                cc.callFunc(function () {
                                    ShowAllCard_LYC(that, 1);
                                })));
                            this.visible = true;
                        }
                    },
                    nfTanPai: function (msg) {
                        var pl = getUIPlayer(1);
                        if (pl && (pl.info.uid == msg.uid)) {
                            pl.mjhand = msg.mjhand;
                            pl.handScore = msg.handScore;
                            pl.hasBiPai = msg.hasBiPai;
                            // ShowAllCard_LYC(this, 1);
                            var that = this;
                            this.runAction(cc.sequence(cc.delayTime(1), 
                                cc.callFunc(function () {
                                    ShowAllCard_LYC(that, 1);
                                })));
                            this.visible = true;
                        }
                    },
                    startCallBank: function () {
                        this.visible = false;
                    }
                }
            }, 
            _event: {
                clearCardUI: function () {
    
                }, 
                initSceneData: function (eD) {
                    SetUserVisible_LYC(this, 1);
    
                }, 
                addPlayer: function (eD) {
                    SetUserVisible_LYC(this, 1);
    
                }, 
                removePlayer: function (eD) {
                    SetUserVisible_LYC(this, 1);
    
                },
                 mjhand: function (eD) {
                    InitUserHandUI_LYC(this, 1);
    
                }, 
                roundEnd: function () {
                    InitUserCoinAndName(this, 1);
    
                }, 
                onlinePlayer: function (msg) {
                    var pl = getUIPlayer(1);
                    if (pl && (pl.info.uid == msg.uid)) {
                        pl.onLine = msg.onLine;
                        setUserOffline(this, 1);
                    }
    
                }
            }
        },
        Node_player3: {
            _layout: [[0.2, 0.315], [0.87, 0.65], [0, 0]], 
            head: {
                AtlasLabel_Score: {
                    _run: function () { this.visible = false; }
                }, 
                ready: {
                    _run: function () {
                        GetReadyVisible(this, 2);
                    }, 
                    _event: {
                        addPlayer: function () {
                            GetReadyVisible(this, 2);
                        }, removePlayer: function () {
                            GetReadyVisible(this, 2);
                        }, NNSendFourCard: function (eD) {
                            this.visible = false;
                        }, onlinePlayer: function () {
                            GetReadyVisible(this, 2);
                        },
                        startCallBank: function() {
                            this.visible = false;
                        }
                    }
                },
                kuang: {
                    _run: function () {
                        this.visible = false;
                    }, 
                    _event: {
                        initSceneData: function (msg) {
                            var pl = getUIPlayer(2);
                            var tData = MjClient.data.sData.tData;
                            if (pl && (tData.uids[tData.zhuang] == pl.info.uid)) {
                                this.visible = true;
                            } else {
                                this.visible = false;
                            }
                        },
                        clearCardUI: function () {
                            this.visible = false;
                        }
                    }
                },
                zhuang: {
                    _run: function () {
                        this.visible = false;
                    }, 
                    _event: {
                        initSceneData: function (msg) {
                            var pl = getUIPlayer(2);
                            var tData = MjClient.data.sData.tData;
                            if (pl && (tData.uids[tData.zhuang] == pl.info.uid)) {
                                this.visible = true;
                            } else {
                                this.visible = false;
                            }
                        },
                        clearCardUI: function () {
                            this.visible = false;
                        }
                    }
                },
                play_tip_NN: {
                    _run: function () {
                        this.zIndex = actionZindex;
                    }, _visible: false, _event: {
                        clearCardUI: function () {
                            this.visible = false;
                        }, mjhand: function (eD) {
                        }, initSceneData: function (eD) {
                        }, startCallBank: function () {
                            this.visible = false;
                        }
                    }
                }, 
                play_Qiang_NN: {
                    _visible: false, _run: function () {
                        this.zIndex = 500;
                    }, _event: {
                        NNCallBank: function (msg) {
                            var pl = getUIPlayer(2);
                            if (pl && (pl.info.uid == msg.uid)) {
                                this.visible = true;
                                this.loadTexture((("poker/niuniu/Sprite_mul" + msg.callBankNum) + ".png"));
                            }
                        }, clearCardUI: function () {
                            this.visible = false;
    
                        }, 
                        // pkTanPai: function (eD) {
                        //     this.visible = false;
                        // }, 
                        mjhand: function (eD) {
                            this.visible = false;
                        }, 
                        waitJiazhu: function (eD) {
                            this.visible = false;
                        }, 
                        initSceneData: function (msg) {
                            var pl = getUIPlayer(2);
                            var sData = MjClient.data.sData;
                            var tData = sData.tData;
                            if ((tData.tState == TableState.waitJiazhu) || (tData.tState == TableState.waitCallBank)) {
                                for (var uid in msg.players) {
                                    if (pl && (pl.info.uid == uid)) {
                                        this.visible = true;
                                        this.loadTexture((("poker/niuniu/Sprite_mul" + msg.players[uid].callBankNum) + ".png"));
                                    }
                                }
                            } else {
                                this.visible = false;
                            }
    
                        }, roundEnd: function () {
                            this.visible = false;
                        }
                    }
                },
                tuoguan: {
                    _run: function () {
                        this.visible = false;
    
                    }, _event: {
                        beTrust: function (msg) {
                            if (getUIPlayer(2) && (getUIPlayer(2).info.uid == msg.uid)) {
                                this.visible = true;
                            }
    
                        }, cancelTrust: function (msg) {
                            if (getUIPlayer(2) && (getUIPlayer(2).info.uid == msg.uid)) {
                                this.visible = false;
                            }
    
                        }, initSceneData: function (msg) {
                            var pl = getUIPlayer(2);
                            if (pl && pl.trust) {
                                this.visible = true;
                            } else {
                                this.visible = false;
                            }
    
                        }
                    }
                }, 
                chatbg: {
                    _run: function () {
                        this.getParent().zIndex = 500;
    
                    }, chattext: {
                        _event: {
                            MJChat: function (msg) {
                                showUserChat(this, 2, msg);
    
                            }, playVoice: function (voicePath) {
                                MjClient.data._tempMessage.msg = voicePath;
                                showUserChat(this, 2, MjClient.data._tempMessage);
    
                            }
                        }
                    }
                }, 
                _click: function (btn) {
                    showPlayerInfo(2, btn);
                },
                _event: {
                    loadWxHead: function (d) {
                        setWxHead(this, d, 2);
    
                    }, addPlayer: function (eD) {
    
                    }, removePlayer: function (eD) {
    
                    }
                }, 
                _run: function () {
                }, 
                jiaZhu: {
                    _run: function () {
                        this.visible = false;
                        this.setString("0");
                        this.ignoreContentAdaptWithSize(true);
    
                    }, 
                    _event: {
                        MJJiazhu: function (msg) {
                            var pl = getUIPlayer(2);
                            if (pl && (msg.uid == pl.info.uid)) {
                                this.visible = true;
                                this.setString(msg.jiazhuNum);
                            }
                        }, 
                        roundEnd: function (eD) {
                            this.visible = false;
                            this.setString("0");
                        }
                    }
                }, 
                gold_icon: {
                    _run: function () {
                        this.visible = false;
                    }, 
                    _event: {
                        roundEnd: function (eD) {
                            this.visible = false;
                        }, 
                        MJJiazhu: function (msg) {
                            var pl = getUIPlayer(2);
                            if (pl && (msg.uid == pl.info.uid)) {
                                this.visible = true;
                            }
                        }
                    }
                },
                play_bi_pai: {
                    _visible: false,
                    _event: {
                        nfTanPai: function (msg) {
                            var pl = getUIPlayer(2);
                            if (pl && (pl.info.uid == msg.uid)) {
                                if (msg.biPaiResult) {
                                    if (msg.biPaiResult > 0){
                                        this.loadTexture("poker/laoyancai/win.png");
                                    } else {
                                        this.loadTexture("poker/laoyancai/lose.png");
                                    }
                                    this.visible = true;
                                }
                            }
                        },
                        clearCardUI: function () {
                            this.visible = false;
                        }
                    }
                },
                compare:{
                    _run:function () { this.visible=false; },
                    _click:function () { 
                        postEvent("endcompare");
                        if ((MjClient.rePlayVideo !== -1)) {
                            return;
                        }
                        var pl = getUIPlayer(2);
                        if (!pl) {
                            return;
                        }
                        var sendMsg = {cmd:"pkCompareCard", uid:pl.info.uid};
                        MjClient.gamenet.request("pkroom.handler.tableMsg",sendMsg); 
                    },
                    _event:{
                        clearCardUI:function () { this.visible=false; },
                        docompare:function () { 
                            var pl = getUIPlayer(2);
                            if (pl && !pl.hasBiPai && !isCenterJoin(2)) {
                                this.visible=true;
                            } 
                        },
                        endcompare:function () { this.visible=false; }
                    }
                }
            }, 
            panel_Cards: {
                _run: function () {
                    this.visible = false;
                }, 
                _event: {
                    NNSendFourCard: function (mjHandMsg) {
                        this.visible = false;
                        var pl = getUIPlayer(2);
                        if (pl && mjHandMsg[pl.info.uid]) {
                            this.visible = true;
                        }
                    }, 
                    initSceneData: function (msg) {
                        this.visible = false;
                        var pl = getUIPlayer(2);
                        if (pl && msg.players[pl.info.uid] && (isCenterJoin(2) == false)) {
                            var tData = MjClient.data.sData.tData;
                            if (tData.tState == TableState.waitCard || tData.tState == TableState.waitZhaPai || tData.tState == TableState.waitLaoPai) {
                                this.visible = true;
                                ShowTwoCard_LYC(this, 2, false);
                            }
                        }
                    }, 
                    pkLaoPai: function (msg) {
                        var pl = getUIPlayer(2);
                        if (pl && msg.uid && (pl.info.uid == msg.uid) && msg.newCard) {
                            ShowLaoCard_LYC(this, 0, msg.newCard, true);
                            this.visible = true;
                        }
                    },
                    pkTanPai: function (msg) {
                        var pl = getUIPlayer(2);
                        if (pl && (pl.info.uid == msg.uid)) {
                            pl.mjhand = msg.mjhand;
                            pl.handScore = msg.handScore;
                            var that = this;
                            this.runAction(cc.sequence(cc.delayTime((lyc_tanpai_time * lyc_tanpai_count++)), 
                                cc.callFunc(function () {
                                    ShowAllCard_LYC(that, 2);
                                })));
                            this.visible = true;
                        }
                    },
                    nfTanPai: function (msg) {
                        var pl = getUIPlayer(2);
                        if (pl && (pl.info.uid == msg.uid)) {
                            pl.mjhand = msg.mjhand;
                            pl.handScore = msg.handScore;
                            pl.hasBiPai = msg.hasBiPai;
                            // ShowAllCard_LYC(this, 2);
                            var that = this;
                            this.runAction(cc.sequence(cc.delayTime(1), 
                                cc.callFunc(function () {
                                    ShowAllCard_LYC(that, 2);
                                })));
                            this.visible = true;
                        }
                    },
                    startCallBank: function () {
                        this.visible = false;
                    }
                }
            }, 
            _event: {
                initSceneData: function (eD) {
                    SetUserVisible_LYC(this, 2);
        
                }, addPlayer: function (eD) {
                    SetUserVisible_LYC(this, 2);
        
                }, removePlayer: function (eD) {
                    SetUserVisible_LYC(this, 2);
        
                }, mjhand: function (eD) {
                    InitUserHandUI_LYC(this, 2);
        
                }, roundEnd: function () {
                    InitUserCoinAndName(this, 2);
                }, 
                onlinePlayer: function (msg) {
                    var pl = getUIPlayer(2);
                    if (pl && (pl.info.uid == msg.uid)) {
                        pl.onLine = msg.onLine;
                        setUserOffline(this, 2);
                    }
                }
            }
        },
        Node_player4: {
            _layout: [[0.2, 0.315], [0.6, 0.82], [0, 0]], 
            head: {
                AtlasLabel_Score: {
                    _run: function () {
                        this.visible = false;
                    }
                }, ready: {
                    _run: function () {
                        GetReadyVisible(this, 3);
                    }, _event: {
                        addPlayer: function () {
                            GetReadyVisible(this, 3);
                        }, removePlayer: function () {
                            GetReadyVisible(this, 3);
                        }, NNSendFourCard: function (eD) {
                            this.visible = false;
                        }, onlinePlayer: function () {
                            GetReadyVisible(this, 3);
                        },
                        startCallBank: function() {
                            this.visible = false;
                        }
                    }
                },
                kuang: {
                    _run: function () {
                        this.visible = false;
                    }, 
                    _event: {
                        initSceneData: function (msg) {
                            var pl = getUIPlayer(3);
                            var tData = MjClient.data.sData.tData;
                            if (pl && (tData.uids[tData.zhuang] == pl.info.uid)) {
                                this.visible = true;
                            } else {
                                this.visible = false;
                            }
                        },
                        clearCardUI: function () {
                            this.visible = false;
                        }
                    }
                },
                zhuang: {
                    _run: function () {
                        this.visible = false;
                    }, 
                    _event: {
                        initSceneData: function (msg) {
                            var pl = getUIPlayer(3);
                            var tData = MjClient.data.sData.tData;
                            if (pl && (tData.uids[tData.zhuang] == pl.info.uid)) {
                                this.visible = true;
                            } else {
                                this.visible = false;
                            }
                        },
                        clearCardUI: function () {
                            this.visible = false;
                        }
                    }
                },
                play_tip_NN: {
                    _run: function () {
                        this.zIndex = actionZindex;
        
                    }, _visible: false, _event: {
                        clearCardUI: function () {
                            this.visible = false;
        
                        }, startCallBank: function () {
                            this.visible = false;
        
                        }
                    }
                }, 
                play_Qiang_NN: {
                    _visible: false, _run: function () {
                        this.zIndex = 500;
        
                    }, _event: {
                        NNCallBank: function (msg) {
                            var pl = getUIPlayer(3);
                            if (pl && (pl.info.uid == msg.uid)) {
                                this.visible = true;
                                this.loadTexture((("poker/niuniu/Sprite_mul" + msg.callBankNum) + ".png"));
                            }
                        }, clearCardUI: function () {
                            this.visible = false;
                        }, 
                        // pkTanPai: function (eD) {
                        //     this.visible = false;
                        // }, 
                        mjhand: function (eD) {
                            this.visible = false;
                        }, 
                        waitJiazhu: function (msg) {
                            this.visible = false;
                        }, 
                        initSceneData: function (msg) {
                            var pl = getUIPlayer(3);
                            var sData = MjClient.data.sData;
                            var tData = sData.tData;
                            if ((tData.tState == TableState.waitJiazhu) || (tData.tState == TableState.waitCallBank)) {
                                for (var uid in msg.players) {
                                    if (pl && (pl.info.uid == uid)) {
                                        this.visible = true;
                                        this.loadTexture((("poker/niuniu/Sprite_mul" + msg.players[uid].callBankNum) + ".png"));
                                    }
                                }
                            } else {
                                this.visible = false;
                            }
        
                        }, roundEnd: function () {
                            this.visible = false;
        
                        }
                    }
                }, 
                tuoguan: {
                    _run: function () {
                        this.visible = false;
        
                    }, _event: {
                        beTrust: function (msg) {
                            if (getUIPlayer(3) && (getUIPlayer(3).info.uid == msg.uid)) {
                                this.visible = true;
                            }
        
                        }, cancelTrust: function (msg) {
                            if (getUIPlayer(3) && (getUIPlayer(3).info.uid == msg.uid)) {
                                this.visible = false;
                            }
        
                        }, initSceneData: function (msg) {
                            var pl = getUIPlayer(3);
                            if (pl && pl.trust) {
                                this.visible = true;
                            } else {
                                this.visible = false;
                            }
        
                        }
                    }
                }, chatbg: {
                    _run: function () {
                        this.getParent().zIndex = 500;
        
                    }, chattext: {
                        _event: {
                            MJChat: function (msg) {
                                showUserChat(this, 3, msg);
        
                            }, playVoice: function (voicePath) {
                                MjClient.data._tempMessage.msg = voicePath;
                                showUserChat(this, 3, MjClient.data._tempMessage);
        
                            }
                        }
                    }
                }, _click: function (btn) {
                    showPlayerInfo(3, btn);
        
                }, _event: {
                    loadWxHead: function (d) {
                        setWxHead(this, d, 3);
        
                    }, addPlayer: function (eD) {
        
                    }, removePlayer: function (eD) {
        
                    }
                }, _run: function () {
                }, 
                jiaZhu: {
                    _run: function () {
                        this.visible = false;
                        this.setString("0");
                        this.ignoreContentAdaptWithSize(true);
                    }, 
                    _event: {
                        MJJiazhu: function (msg) {
                            var pl = getUIPlayer(3);
                            if (pl && (msg.uid == pl.info.uid)) {
                                this.visible = true;
                                this.setString(msg.jiazhuNum);
                            }
                        }, 
                        roundEnd: function (eD) {
                            this.visible = false;
                            this.setString("0");
                        }
                    }
                }, 
                gold_icon: {
                    _run: function () {
                        this.visible = false;
        
                    }, 
                    _event: {
                        roundEnd: function (eD) {
                            this.visible = false;
                        }, 
                        MJJiazhu: function (msg) {
                            var pl = getUIPlayer(3);
                            if (pl && (msg.uid == pl.info.uid)) {
                                this.visible = true;
                            }
                        }
                    }
                },
                play_bi_pai: {
                    _visible: false,
                    _event: {
                        nfTanPai: function (msg) {
                            var pl = getUIPlayer(3);
                            if (pl && (pl.info.uid == msg.uid)) {
                                if (msg.biPaiResult) {
                                    if (msg.biPaiResult > 0){
                                        this.loadTexture("poker/laoyancai/win.png");
                                    } else {
                                        this.loadTexture("poker/laoyancai/lose.png");
                                    }
                                    this.visible = true;
                                }
                            }
                        },
                        clearCardUI: function () {
                            this.visible = false;
                        }
                    }
                },
                compare:{
                    _run:function () { this.visible=false; },
                    _click:function () { 
                        postEvent("endcompare");
                        if ((MjClient.rePlayVideo !== -1)) {
                            return;
                        }
                        var pl = getUIPlayer(3);
                        if (!pl) {
                            return;
                        }
                        var sendMsg = {cmd:"pkCompareCard", uid:pl.info.uid};
                        MjClient.gamenet.request("pkroom.handler.tableMsg",sendMsg); 
                    },
                    _event:{
                        clearCardUI:function () { this.visible=false; },
                        docompare:function () { 
                            var pl = getUIPlayer(3);
                            if (pl && !pl.hasBiPai && !isCenterJoin(3)) {
                                this.visible=true;
                            } 
                        },
                        endcompare:function () { this.visible=false; }
                    }
                }
            }, 
            panel_Cards: {
                _run: function () {
                    this.visible = false;
                }, 
                _event: {
                    NNSendFourCard: function (mjHandMsg) {
                        this.visible = false;
                        var pl = getUIPlayer(3);
                        if (pl && mjHandMsg[pl.info.uid]) {
                            this.visible = true;
                        }
    
                    }, 
                    initSceneData: function (msg) {
                        this.visible = false;
                        var pl = getUIPlayer(3);
                        if (pl && msg.players[pl.info.uid] && (isCenterJoin(3) == false)) {
                            var tData = MjClient.data.sData.tData;
                            if (tData.tState == TableState.waitCard || tData.tState == TableState.waitZhaPai || tData.tState == TableState.waitLaoPai) {
                                this.visible = true;
                                ShowTwoCard_LYC(this, 3, false);
                            }
                        }
                    }, 
                    pkLaoPai: function (msg) {
                        var pl = getUIPlayer(3);
                        if (pl && msg.uid && (pl.info.uid == msg.uid) && msg.newCard) {
                            ShowLaoCard_LYC(this, 0, msg.newCard, true);
                        }
                    },
                    pkTanPai: function (msg) {
                        var pl = getUIPlayer(3);
                        if (pl && (pl.info.uid == msg.uid)) {
                            pl.mjhand = msg.mjhand;
                            pl.handScore = msg.handScore;
                            var that = this;
                            this.runAction(cc.sequence(cc.delayTime((lyc_tanpai_time * lyc_tanpai_count++)), 
                                cc.callFunc(function () {
                                    ShowAllCard_LYC(that, 3);
                                })));
                            this.visible = true;
                        }
                    },
                    nfTanPai: function (msg) {
                        var pl = getUIPlayer(3);
                        if (pl && (pl.info.uid == msg.uid)) {
                            pl.mjhand = msg.mjhand;
                            pl.handScore = msg.handScore;
                            pl.hasBiPai = msg.hasBiPai;
                            // ShowAllCard_LYC(this, 3);
                            var that = this;
                            this.runAction(cc.sequence(cc.delayTime(1), 
                                cc.callFunc(function () {
                                    ShowAllCard_LYC(that, 3);
                                })));
                            this.visible = true;
                        }
                    },
                    startCallBank: function () {
                        this.visible = false;
                    }
                }
            }, 
            _event: {
            clearCardUI: function () {
    
            }, initSceneData: function (eD) {
                SetUserVisible_LYC(this, 3);
    
            }, addPlayer: function (eD) {
                SetUserVisible_LYC(this, 3);
    
            }, removePlayer: function (eD) {
                SetUserVisible_LYC(this, 3);
    
            }, mjhand: function (eD) {
                InitUserHandUI_LYC(this, 3);
    
            }, roundEnd: function () {
                InitUserCoinAndName(this, 3);
    
            }, onlinePlayer: function (msg) {
                var pl = getUIPlayer(3);
                if (pl && (pl.info.uid == msg.uid)) {
                    pl.onLine = msg.onLine;
                    setUserOffline(this, 3);
                }
    
                }
            }
        },
        Node_player5: {
            _layout: [[0.2, 0.315], [0.4, 0.82], [0, 0]], 
            head: {
                AtlasLabel_Score: {
                    _run: function () {
                        this.visible = false;
                    }
                }, ready: {
                    _run: function () {
                        GetReadyVisible(this, 4);
                    }, _event: {
                        addPlayer: function () {
                            GetReadyVisible(this, 4);
                        }, removePlayer: function () {
                            GetReadyVisible(this, 4);
                        }, NNSendFourCard: function (eD) {
                            this.visible = false;
                        }, onlinePlayer: function () {
                            GetReadyVisible(this, 4);
                        },
                        startCallBank: function() {
                            this.visible = false;
                        }
                    }
                },
                kuang: {
                    _run: function () {
                        this.visible = false;
                    }, 
                    _event: {
                        initSceneData: function (msg) {
                            var pl = getUIPlayer(4);
                            var tData = MjClient.data.sData.tData;
                            if (pl && (tData.uids[tData.zhuang] == pl.info.uid)) {
                                this.visible = true;
                            } else {
                                this.visible = false;
                            }
                        },
                        clearCardUI: function () {
                            this.visible = false;
                        }
                    }
                }, 
                zhuang: {
                    _run: function () {
                        this.visible = false;
                    }, 
                    _event: {
                        initSceneData: function (msg) {
                            var pl = getUIPlayer(4);
                            var tData = MjClient.data.sData.tData;
                            if (pl && (tData.uids[tData.zhuang] == pl.info.uid)) {
                                this.visible = true;
                            } else {
                                this.visible = false;
                            }
                        },
                        clearCardUI: function () {
                            this.visible = false;
                        }
                    }
                }, 
                play_tip_NN: {
                    _run: function () {
                        this.zIndex = actionZindex;
        
                    }, _visible: false, _event: {
                        clearCardUI: function () {
                            this.visible = false;
        
                        }, mjhand: function (eD) {
        
                        }, initSceneData: function (eD) {
        
                        }, startCallBank: function () {
                            this.visible = false;
        
                        }
                    }
                }, 
                play_Qiang_NN: {
                    _visible: false, _run: function () {
                        this.zIndex = 500;
        
                    }, _event: {
                        NNCallBank: function (msg) {
                            var pl = getUIPlayer(4);
                            if (pl && (pl.info.uid == msg.uid)) {
                                this.visible = true;
                                this.loadTexture((("poker/niuniu/Sprite_mul" + msg.callBankNum) + ".png"));
                            }
                        }, clearCardUI: function () {
                            this.visible = false;
                        }, 
                        // pkTanPai: function (eD) {
                        //     this.visible = false;
                        // }, 
                        mjhand: function (eD) {
                            this.visible = false;
                        }, waitJiazhu: function (eD) {
                            this.visible = false;
                        }, 
                        initSceneData: function (msg) {
                                var pl = getUIPlayer(4);
                                var sData = MjClient.data.sData;
                                var tData = sData.tData;
                                if ((tData.tState == TableState.waitJiazhu) || (tData.tState == TableState.waitCallBank)) {
                                    for (var uid in msg.players) {
                                        if (pl && (pl.info.uid == uid)) {
                                            this.visible = true;
                                            this.loadTexture((("poker/niuniu/Sprite_mul" + msg.players[uid].callBankNum) + ".png"));
                                        }
                                    }
                                } else {
                                    this.visible = false;
                                }
        
                        }, roundEnd: function () {
                            this.visible = false;
        
                        }
                    }
                }, 
                tuoguan: {
                    _run: function () {
                        this.visible = false;
        
                    }, _event: {
                        beTrust: function (msg) {
                            if (getUIPlayer(4) && (getUIPlayer(4).info.uid == msg.uid)) {
                                this.visible = true;
                            }
        
                        }, cancelTrust: function (msg) {
                            if (getUIPlayer(4) && (getUIPlayer(4).info.uid == msg.uid)) {
                                this.visible = false;
                            }
        
                        }, initSceneData: function (msg) {
                            var pl = getUIPlayer(4);
                            if (pl && pl.trust) {
                                this.visible = true;
                            } else {
                                this.visible = false;
                            }
        
                        }
                    }
                }, chatbg: {
                    _run: function () {
                        this.getParent().zIndex = 500;
        
                    }, chattext: {
                        _event: {
                            MJChat: function (msg) {
                                showUserChat(this, 4, msg);
        
                            }, playVoice: function (voicePath) {
                                MjClient.data._tempMessage.msg = voicePath;
                                showUserChat(this, 4, MjClient.data._tempMessage);
        
                            }
                        }
                    }
                }, _click: function (btn) {
                    showPlayerInfo(4, btn);
        
                }, _event: {
                    loadWxHead: function (d) {
                        setWxHead(this, d, 4);
        
                    }, addPlayer: function (eD) {
        
                    }, removePlayer: function (eD) {
        
                    }
                }, _run: function () {
                }, 
                jiaZhu: {
                    _run: function () {
                        this.visible = false;
                        this.setString("0");
                        this.ignoreContentAdaptWithSize(true);
                    }, 
                    _event: {
                        MJJiazhu: function (msg) {
                            var pl = getUIPlayer(4);
                            if (pl && (msg.uid == pl.info.uid)) {
                                this.visible = true;
                                this.setString(msg.jiazhuNum);
                            }
                        }, 
                        roundEnd: function (eD) {
                            this.visible = false;
                            this.setString("0");
                        }
                    }
                }, 
                gold_icon: {
                    _run: function () {
                        this.visible = false;
                    }, 
                    _event: {
                        roundEnd: function (eD) {
                            this.visible = false;
                        }, 
                        MJJiazhu: function (msg) {
                            var pl = getUIPlayer(4);
                            if (pl && (msg.uid == pl.info.uid)) {
                                this.visible = true;
                            }
                        }
                    }
                },
                play_bi_pai: {
                    _visible: false,
                    _event: {
                        nfTanPai: function (msg) {
                            var pl = getUIPlayer(4);
                            if (pl && (pl.info.uid == msg.uid)) {
                                if (msg.biPaiResult) {
                                    if (msg.biPaiResult > 0){
                                        this.loadTexture("poker/laoyancai/win.png");
                                    } else {
                                        this.loadTexture("poker/laoyancai/lose.png");
                                    }
                                    this.visible = true;
                                }
                            }
                        },
                        clearCardUI: function () {
                            this.visible = false;
                        }
                    }
                },
                compare:{
                    _run:function () { this.visible=false; },
                    _click:function () { 
                        postEvent("endcompare");
                        if ((MjClient.rePlayVideo !== -1)) {
                            return;
                        }
                        var pl = getUIPlayer(4);
                        if (!pl) {
                            return;
                        }
                        var sendMsg = {cmd:"pkCompareCard", uid:pl.info.uid};
                        MjClient.gamenet.request("pkroom.handler.tableMsg",sendMsg); 
                    },
                    _event:{
                        clearCardUI:function () { this.visible=false; },
                        docompare:function () { 
                            var pl = getUIPlayer(4);
                            if (pl && !pl.hasBiPai && !isCenterJoin(4)) {
                                this.visible=true;
                            } 
                        },
                        endcompare:function () { this.visible=false; }
                    }
                }
            }, 
            panel_Cards: {
                _run: function () {
                    this.visible = false;
                }, 
                _event: {
                    NNSendFourCard: function (mjHandMsg) {
                        this.visible = false;
                        var pl = getUIPlayer(4);
                        if (pl && mjHandMsg[pl.info.uid]) {
                            this.visible = true;
                        }
    
                    }, 
                    initSceneData: function (msg) {
                        this.visible = false;
                        var pl = getUIPlayer(4);
                        if (pl && msg.players[pl.info.uid] && (isCenterJoin(4) == false)) {
                            var tData = MjClient.data.sData.tData;
                            if (tData.tState == TableState.waitCard || tData.tState == TableState.waitZhaPai || tData.tState == TableState.waitLaoPai) {
                                this.visible = true;
                                ShowTwoCard_LYC(this, 4, false);
                            }
                        }
                    }, 
                    pkLaoPai: function (msg) {
                        var pl = getUIPlayer(4);
                        if (pl && msg.uid && (pl.info.uid == msg.uid) && msg.newCard) {
                            ShowLaoCard_LYC(this, 0, msg.newCard, true);
                        }
                    },
                    pkTanPai: function (msg) {
                        var pl = getUIPlayer(4);
                        if (pl && (pl.info.uid == msg.uid)) {
                            pl.mjhand = msg.mjhand;
                            pl.handScore = msg.handScore;
                            var that = this;
                            this.runAction(cc.sequence(cc.delayTime((lyc_tanpai_time * lyc_tanpai_count++)), 
                                cc.callFunc(function () {
                                    ShowAllCard_LYC(that, 4);
                                })));
                            this.visible = true;
                        }
                    },
                    nfTanPai: function (msg) {
                        var pl = getUIPlayer(4);
                        if (pl && (pl.info.uid == msg.uid)) {
                            pl.mjhand = msg.mjhand;
                            pl.handScore = msg.handScore;
                            pl.hasBiPai = msg.hasBiPai;
                            // ShowAllCard_LYC(this, 4);
                            var that = this;
                            this.runAction(cc.sequence(cc.delayTime(1), 
                                cc.callFunc(function () {
                                    ShowAllCard_LYC(that, 4);
                                })));
                            this.visible = true;
                        }
                    },
                    startCallBank: function () {
                        this.visible = false;
                    }
                }
            }, 
            _event: {
            clearCardUI: function () {
    
            }, initSceneData: function (eD) {
                SetUserVisible_LYC(this, 4);
    
            }, addPlayer: function (eD) {
                SetUserVisible_LYC(this, 4);
    
            }, removePlayer: function (eD) {
                SetUserVisible_LYC(this, 4);
    
            }, mjhand: function (eD) {
                InitUserHandUI_LYC(this, 4);
    
            }, roundEnd: function () {
                InitUserCoinAndName(this, 4);
    
            }, onlinePlayer: function (msg) {
                var pl = getUIPlayer(4);
                if (pl && (pl.info.uid == msg.uid)) {
                    pl.onLine = msg.onLine;
                    setUserOffline(this, 4);
                }
    
                }
            }
        },
        Node_player6: {
            _layout: [[0.2, 0.315], [0.13, 0.65], [0, 0]], 
            head: {
                AtlasLabel_Score: {
                    _run: function () {
                        this.visible = false;
                    }
                }, ready: {
                    _run: function () {
                        GetReadyVisible(this, 5);
                    }, _event: {
                        addPlayer: function () {
                            GetReadyVisible(this, 5);
                        }, removePlayer: function () {
                            GetReadyVisible(this, 5);
                        }, NNSendFourCard: function (eD) {
                            this.visible = false;
                        }, onlinePlayer: function () {
                            GetReadyVisible(this, 5);
                        },
                        startCallBank: function() {
                            this.visible = false;
                        }
                    }
                }, 
                kuang: {
                    _run: function () {
                        this.visible = false;
                    }, 
                    _event: {
                        initSceneData: function (msg) {
                            var pl = getUIPlayer(5);
                            var tData = MjClient.data.sData.tData;
                            if (pl && (tData.uids[tData.zhuang] == pl.info.uid)) {
                                this.visible = true;
                            } else {
                                this.visible = false;
                            }
                        },
                        clearCardUI: function () {
                            this.visible = false;
                        }
                    }
                }, 
                zhuang: {
                    _run: function () {
                        this.visible = false;
                    }, 
                    _event: {
                        initSceneData: function (msg) {
                            var pl = getUIPlayer(5);
                            var tData = MjClient.data.sData.tData;
                            if (pl && (tData.uids[tData.zhuang] == pl.info.uid)) {
                                this.visible = true;
                            } else {
                                this.visible = false;
                            }
                        },
                        clearCardUI: function () {
                            this.visible = false;
                        }
                    }
                }, 
                play_tip_NN: {
                    _run: function () {
                        this.zIndex = actionZindex;
                    }, 
                    _visible: false, 
                    _event: {
                        clearCardUI: function () {
                            this.visible = false;
        
                        }, initSceneData: function (eD) {
        
                        }, startCallBank: function () {
                            this.visible = false;
        
                        }
                    }
                }, 
                play_Qiang_NN: {
                    _visible: false, 
                    _run: function () {
                        this.zIndex = 500;
                    }, 
                    _event: {
                        NNCallBank: function (msg) {
                            var pl = getUIPlayer(5);
                            if (pl && (pl.info.uid == msg.uid)) {
                                this.visible = true;
                                this.loadTexture((("poker/niuniu/Sprite_mul" + msg.callBankNum) + ".png"));
                            }
                        }, clearCardUI: function () {
                            this.visible = false;
                        }, 
                        // pkTanPai: function (eD) {
                        //     this.visible = false;
                        // }, 
                        mjhand: function (eD) {
                            this.visible = false;
                        }, waitJiazhu: function (eD) {
                            this.visible = false;
                        }, 
                        initSceneData: function (msg) {
                            var pl = getUIPlayer(5);
                            var sData = MjClient.data.sData;
                            var tData = sData.tData;
                            if ((tData.tState == TableState.waitJiazhu) || (tData.tState == TableState.waitCallBank)) {
                                for (var uid in msg.players) {
                                    if (pl && (pl.info.uid == uid)) {
                                        this.visible = true;
                                        this.loadTexture((("poker/niuniu/Sprite_mul" + msg.players[uid].callBankNum) + ".png"));
                                    }
                                }
                            } else {
                                this.visible = false;
                            }
        
                        }, roundEnd: function () {
                            this.visible = false;
        
                        }
                    }
                }, 
                tuoguan: {
                    _run: function () {
                        this.visible = false;
        
                    }, _event: {
                        beTrust: function (msg) {
                            if (getUIPlayer(5) && (getUIPlayer(5).info.uid == msg.uid)) {
                                this.visible = true;
                            }
        
                        }, cancelTrust: function (msg) {
                            if (getUIPlayer(5) && (getUIPlayer(5).info.uid == msg.uid)) {
                                this.visible = false;
                            }
        
                        }, initSceneData: function (msg) {
                            var pl = getUIPlayer(5);
                            if (pl && pl.trust) {
                                this.visible = true;
                            } else {
                                this.visible = false;
                            }
        
                        }
                    }
                }, 
                chatbg: {
                    _run: function () {
                        this.getParent().zIndex = 500;
        
                    }, chattext: {
                        _event: {
                            MJChat: function (msg) {
                                showUserChat(this, 1, msg);
        
                            }, playVoice: function (voicePath) {
                                MjClient.data._tempMessage.msg = voicePath;
                                showUserChat(this, 5, MjClient.data._tempMessage);
        
                            }
                        }
                    }
                }, 
                _click: function (btn) {
                    showPlayerInfo(5, btn);
                }, 
                jiaZhu: {
                    _run: function () {
                        this.visible = false;
                        this.setString("0");
                        this.ignoreContentAdaptWithSize(true);
                    }, 
                    _event: {
                        MJJiazhu: function (msg) {
                            var pl = getUIPlayer(5);
                            if (pl && (msg.uid == pl.info.uid)) {
                                this.visible = true;
                                this.setString(msg.jiazhuNum);
                            }
                        }, 
                        roundEnd: function (eD) {
                            this.visible = false;
                            this.setString("0");
                        }
                    }
                }, 
                gold_icon: {
                    _run: function () {
                        this.visible = false;
                    }, 
                    _event: {
                        roundEnd: function (eD) {
                            this.visible = false;
                        }, 
                        MJJiazhu: function (msg) {
                            var pl = getUIPlayer(5);
                            if (pl && (msg.uid == pl.info.uid)) {
                                this.visible = true;
                            }
                        }
                    }
                }, 
                play_bi_pai: {
                    _visible: false,
                    _event: {
                        nfTanPai: function (msg) {
                            var pl = getUIPlayer(5);
                            if (pl && (pl.info.uid == msg.uid)) {
                                if (msg.biPaiResult) {
                                    if (msg.biPaiResult > 0){
                                        this.loadTexture("poker/laoyancai/win.png");
                                    } else {
                                        this.loadTexture("poker/laoyancai/lose.png");
                                    }
                                    this.visible = true;
                                }
                            }
                        },
                        clearCardUI: function () {
                            this.visible = false;
                        }
                    }
                },
                compare:{
                    _run:function () { this.visible=false; },
                    _click:function () { 
                        postEvent("endcompare");
                        if ((MjClient.rePlayVideo !== -1)) {
                            return;
                        }
                        var pl = getUIPlayer(5);
                        if (!pl) {
                            return;
                        }
                        var sendMsg = {cmd:"pkCompareCard", uid:pl.info.uid};
                        MjClient.gamenet.request("pkroom.handler.tableMsg",sendMsg); 
                    },
                    _event:{
                        clearCardUI:function () { this.visible=false; },
                        docompare:function () { 
                            var pl = getUIPlayer(5);
                            if (pl && !pl.hasBiPai && !isCenterJoin(5)) {
                                this.visible=true;
                            } 
                        },
                        endcompare:function () { this.visible=false; }
                    }
                },
                _run: function () {
                }, 
                _event: {
                    loadWxHead: function (d) {
                        setWxHead(this, d, 5);
                    }, addPlayer: function (eD) {
                    }, removePlayer: function (eD) {
                    }
                }
            }, 
            panel_Cards: {
                _run: function () {
                    this.visible = false;
                }, 
                _event: {
                    NNSendFourCard: function (mjHandMsg) {
                        this.visible = false;
                        var pl = getUIPlayer(5);
                        if (pl && mjHandMsg[pl.info.uid]) {
                            this.visible = true;
                        }
    
                    }, 
                    initSceneData: function (msg) {
                        this.visible = false;
                        var pl = getUIPlayer(5);
                        if (pl && msg.players[pl.info.uid] && (isCenterJoin(5) == false)) {
                            var tData = MjClient.data.sData.tData;
                            if (tData.tState == TableState.waitCard || tData.tState == TableState.waitZhaPai || tData.tState == TableState.waitLaoPai) {
                                this.visible = true;
                                ShowTwoCard_LYC(this, 5, false);
                            }
                        }
                    }, 
                    pkLaoPai: function (msg) {
                        var pl = getUIPlayer(5);
                        if (pl && msg.uid && (pl.info.uid == msg.uid) && msg.newCard) {
                            ShowLaoCard_LYC(this, 0, msg.newCard, true);
                        }
                    },
                    pkTanPai: function (msg) {
                        var pl = getUIPlayer(5);
                        if (pl && (pl.info.uid == msg.uid)) {
                            pl.mjhand = msg.mjhand;
                            pl.handScore = msg.handScore;
                            var that = this;
                            this.runAction(cc.sequence(cc.delayTime((lyc_tanpai_time * lyc_tanpai_count++)), 
                                cc.callFunc(function () {
                                    ShowAllCard_LYC(that, 5);
                                })));
                            this.visible = true;
                        }
                    },
                    nfTanPai: function (msg) {
                        var pl = getUIPlayer(5);
                        if (pl && (pl.info.uid == msg.uid)) {
                            pl.mjhand = msg.mjhand;
                            pl.handScore = msg.handScore;
                            pl.hasBiPai = msg.hasBiPai;
                            // ShowAllCard_LYC(this, 5);
                            var that = this;
                            this.runAction(cc.sequence(cc.delayTime(1), 
                                cc.callFunc(function () {
                                    ShowAllCard_LYC(that, 5);
                                })));
                            this.visible = true;
                        }
                    },
                    startCallBank: function () {
                        this.visible = false;
                    }
                }
            }, 
            _event: {
            initSceneData: function (eD) {
                SetUserVisible_LYC(this, 5);
    
            }, addPlayer: function (eD) {
                SetUserVisible_LYC(this, 5);
    
            }, removePlayer: function (eD) {
                SetUserVisible_LYC(this, 5);
    
            }, mjhand: function (eD) {
                InitUserHandUI_LYC(this, 5);
    
            }, roundEnd: function () {
                InitUserCoinAndName(this, 5);
    
            }, onlinePlayer: function (msg) {
                var pl = getUIPlayer(5);
                if (pl && (pl.info.uid == msg.uid)) {
                    pl.onLine = msg.onLine;
                    setUserOffline(this, 5);
                }
    
                }
            }
        },
        Node_player7: {
            _layout: [[0.2, 0.315], [0.13, 0.40], [0, 0]], 
            head: {
                AtlasLabel_Score: {
                    _run: function () {
                        this.visible = false;
    
                    }
                }, ready: {
                    _run: function () {
                        GetReadyVisible(this, 6);
                    }, _event: {
                        addPlayer: function () {
                            GetReadyVisible(this, 6);
                        }, removePlayer: function () {
                            GetReadyVisible(this, 6);
                        }, NNSendFourCard: function (eD) {
                            this.visible = false;
                        }, onlinePlayer: function () {
                            GetReadyVisible(this, 6);
                        },
                        startCallBank: function() {
                            this.visible = false;
                        }
                    }
                }, 
                kuang: {
                    _run: function () {
                        this.visible = false;
                    }, _event: {
                        initSceneData: function (msg) {
                            var pl = getUIPlayer(6);
                            var tData = MjClient.data.sData.tData;
                            if (pl && (tData.uids[tData.zhuang] == pl.info.uid)) {
                                this.visible = true;
                            } else {
                                this.visible = false;
                            }
                        },
                        clearCardUI: function () {
                            this.visible = false;
                        }
                    }
                }, 
                zhuang: {
                    _run: function () {
                        this.visible = false;
                    }, _event: {
                        initSceneData: function (msg) {
                            var pl = getUIPlayer(6);
                            var tData = MjClient.data.sData.tData;
                            if (pl && (tData.uids[tData.zhuang] == pl.info.uid)) {
                                this.visible = true;
                            } else {
                                this.visible = false;
                            }
                        },
                        clearCardUI: function () {
                            this.visible = false;
                        }
                    }
                }, 
                play_tip_NN: {
                    _visible: false, 
                    _run: function () {
                        this.zIndex = actionZindex;
                    }, 
                    _event: {
                        clearCardUI: function () {
                            this.visible = false;
                        }, startCallBank: function () {
                            this.visible = false;
                        }
                    }
                }, 
                play_Qiang_NN: {
                    _visible: false, 
                    _run: function () {
                        this.zIndex = 500;
                    }, 
                    _event: {
                            NNCallBank: function (msg) {
                                var pl = getUIPlayer(6);
                                if (pl && (pl.info.uid == msg.uid)) {
                                    this.visible = true;
                                    this.loadTexture((("poker/niuniu/Sprite_mul" + msg.callBankNum) + ".png"));
                                }
                            }, clearCardUI: function () {
                                this.visible = false;
                            }, 
                            // pkTanPai: function (eD) {
                            //     this.visible = false;
                            // }, 
                            mjhand: function (eD) {
                                this.visible = false;
                            }, 
                            waitJiazhu: function (eD) {
                                this.visible = false;
                            }, 
                            initSceneData: function (msg) {
                                var pl = getUIPlayer(6);
                                var sData = MjClient.data.sData;
                                var tData = sData.tData;
                                if ((tData.tState == TableState.waitJiazhu) || (tData.tState == TableState.waitCallBank)) {
                                    for (var uid in msg.players) {
                                        if (pl && (pl.info.uid == uid)) {
                                            this.visible = true;
                                            this.loadTexture((("poker/niuniu/Sprite_mul" + msg.players[uid].callBankNum) + ".png"));
                                        }
                                    }
                                } else {
                                    this.visible = false;
                                }
            
                            }, roundEnd: function () {
                                this.visible = false;
                            }
                    }
                }, 
                tuoguan: {
                    _run: function () {
                        this.visible = false;
        
                    }, _event: {
                        beTrust: function (msg) {
                            if (getUIPlayer(6) && (getUIPlayer(6).info.uid == msg.uid)) {
                                this.visible = true;
                            }
        
                        }, cancelTrust: function (msg) {
                            if (getUIPlayer(6) && (getUIPlayer(6).info.uid == msg.uid)) {
                                this.visible = false;
                            }
        
                        }, initSceneData: function (msg) {
                            var pl = getUIPlayer(6);
                            if (pl && pl.trust) {
                                this.visible = true;
                            } else {
                                this.visible = false;
                            }
        
                        }
                    }
                }, chatbg: {
                    _run: function () {
                        this.getParent().zIndex = 500;
        
                    }, chattext: {
                        _event: {
                            MJChat: function (msg) {
                                showUserChat(this, 6, msg);
        
                            }, playVoice: function (voicePath) {
                                MjClient.data._tempMessage.msg = voicePath;
                                showUserChat(this, 6, MjClient.data._tempMessage);
        
                            }
                        }
                    }
                }, _click: function (btn) {
                    showPlayerInfo(6, btn);
                }, _event: {
                    loadWxHead: function (d) {
                        setWxHead(this, d, 6);
        
                    }, addPlayer: function (eD) {
        
                    }, removePlayer: function (eD) {
        
                    }
                }, _run: function () {
                }, 
                jiaZhu: {
                    _run: function () {
                        this.visible = false;
                        this.setString("0");
                        this.ignoreContentAdaptWithSize(true);
                    }, 
                    _event: {
                        MJJiazhu: function (msg) {
                            var pl = getUIPlayer(6);
                            if (pl && (msg.uid == pl.info.uid)) {
                                this.visible = true;
                                this.setString(msg.jiazhuNum);
                            }
                        }, 
                        roundEnd: function (eD) {
                            this.visible = false;
                            this.setString("0");
                        }
                    }
                }, 
                gold_icon: {
                    _run: function () {
                        this.visible = false;
                    }, 
                    _event: {
                        roundEnd: function (eD) {
                            this.visible = false;
                        }, 
                        MJJiazhu: function (msg) {
                            var pl = getUIPlayer(6);
                            if (pl && (msg.uid == pl.info.uid)) {
                                this.visible = true;
                            }
                        }
                    }
                },
                play_bi_pai: {
                    _visible: false,
                    _event: {
                        nfTanPai: function (msg) {
                            var pl = getUIPlayer(6);
                            if (pl && (pl.info.uid == msg.uid)) {
                                if (msg.biPaiResult) {
                                    if (msg.biPaiResult > 0){
                                        this.loadTexture("poker/laoyancai/win.png");
                                    } else {
                                        this.loadTexture("poker/laoyancai/lose.png");
                                    }
                                    this.visible = true;
                                }
                            }
                        },
                        clearCardUI: function () {
                            this.visible = false;
                        }
                    }
                },
                compare:{
                    _run:function () { this.visible=false; },
                    _click:function () { 
                        postEvent("endcompare");
                        if ((MjClient.rePlayVideo !== -1)) {
                            return;
                        }
                        var pl = getUIPlayer(6);
                        if (!pl) {
                            return;
                        }
                        var sendMsg = {cmd:"pkCompareCard", uid:pl.info.uid};
                        MjClient.gamenet.request("pkroom.handler.tableMsg",sendMsg); 
                    },
                    _event:{
                        clearCardUI:function () { this.visible=false; },
                        docompare:function () { 
                            var pl = getUIPlayer(6);
                            if (pl && !pl.hasBiPai && !isCenterJoin(6)) {
                                this.visible=true;
                            } 
                        },
                        endcompare:function () { this.visible=false; }
                    }
                }
            }, 
            panel_Cards: {
                _run: function () {
                    this.visible = false;
                }, 
                _event: {
                    NNSendFourCard: function (mjHandMsg) {
                        this.visible = false;
                        var pl = getUIPlayer(6);
                        if (pl && mjHandMsg[pl.info.uid]) {
                            this.visible = true;
                        }
    
                    }, 
                    initSceneData: function (msg) {
                        this.visible = false;
                        var pl = getUIPlayer(6);
                        if (pl && msg.players[pl.info.uid] && (isCenterJoin(6) == false)) {
                            var tData = MjClient.data.sData.tData;
                            if (tData.tState == TableState.waitCard || tData.tState == TableState.waitZhaPai || tData.tState == TableState.waitLaoPai) {
                                this.visible = true;
                                ShowTwoCard_LYC(this, 6, false);
                            }
                        }
                    }, 
                    pkLaoPai: function (msg) {
                        var pl = getUIPlayer(6);
                        if (pl && msg.uid && (pl.info.uid == msg.uid) && msg.newCard) {
                            ShowLaoCard_LYC(this, 0, msg.newCard, true);
                        }
                    },
                    pkTanPai: function (msg) {
                        var pl = getUIPlayer(6);
                        if (pl && (pl.info.uid == msg.uid)) {
                            pl.mjhand = msg.mjhand;
                            pl.handScore = msg.handScore;
                            var that = this;
                            this.runAction(cc.sequence(cc.delayTime((lyc_tanpai_time * lyc_tanpai_count++)), 
                                cc.callFunc(function () {
                                    ShowAllCard_LYC(that, 6);
                                })));
                            this.visible = true;
                        }
                    },
                    nfTanPai: function (msg) {
                        var pl = getUIPlayer(6);
                        if (pl && (pl.info.uid == msg.uid)) {
                            pl.mjhand = msg.mjhand;
                            pl.handScore = msg.handScore;
                            pl.hasBiPai = msg.hasBiPai;
                            // ShowAllCard_LYC(this, 6);
                            var that = this;
                            this.runAction(cc.sequence(cc.delayTime(1), 
                                cc.callFunc(function () {
                                    ShowAllCard_LYC(that, 6);
                                })));
                            this.visible = true;
                        }
                    },
                    startCallBank: function () {
                        this.visible = false;
                    }
                }
            }, 
            _event: {
                clearCardUI: function () {
        
                }, initSceneData: function (eD) {
                    SetUserVisible_LYC(this, 6);
        
                }, addPlayer: function (eD) {
                    SetUserVisible_LYC(this, 6);
        
                }, removePlayer: function (eD) {
                    SetUserVisible_LYC(this, 6);
        
                }, mjhand: function (eD) {
                    InitUserHandUI_LYC(this, 6);
        
                }, roundEnd: function () {
                    InitUserCoinAndName(this, 6);
        
                }, onlinePlayer: function (msg) {
                    var pl = getUIPlayer(6);
                    if (pl && (pl.info.uid == msg.uid)) {
                        pl.onLine = msg.onLine;
                        setUserOffline(this, 6);
                    }
                }
            }
        },
        chat_btn: {
            _layout: [[0.09, 0.09], [0.97, 0.1665], [0, 0]], 
            _run: function () {
                this.visible = true;
            }, 
            _click: function () {
                var chatlayer = new ChatLayer();
                MjClient.Scene.addChild(chatlayer);
            }
        },
        voice_btn: {
            _layout: [[0.09, 0.09], [0.97, 0.0658], [0, 0]],
            _run: function () {
                this.visible = false;
                initVoiceData();
                cc.eventManager.addListener(getTouchListener(), this);
                if (MjClient.isShenhe) {
                    this.visible = false;
                }
            }, 
            _touch: function (btn, eT) {
                if ((eT == 0)) {
                    startRecord();
                } else if ((eT == 2)) {
                    endRecord();
                } else if ((eT == 3)) {
                    cancelRecord();
                }
            }, 
            _event: {
                cancelRecord: function () {
                    MjClient.native.HelloOC("cancelRecord !!!");
                },
                uploadRecord: function (filePath) {
                    if (filePath) {
                        MjClient.native.HelloOC("upload voice file");
                        MjClient.native.UploadFile(filePath, MjClient.remoteCfg.voiceUrl, "sendVoice");
                    } else {
                        MjClient.native.HelloOC("No voice file update");
                    }
    
                },
                sendVoice: function (fullFilePath) {
                    if (!(fullFilePath)) {
                        console.log("sendVoice No fileName");
                        return;
                    }
                    var getFileName = /[^\/]+$/;
                    var extensionName = getFileName.exec(fullFilePath);
                    var fileName = extensionName[(extensionName.length - 1)];
                    console.log(("sfileName is:" + fileName));
                    MjClient.gamenet.request("pkroom.handler.tableMsg", { cmd: "downAndPlayVoice", uid: SelfUid(), type: 3, msg: fileName, num: MjClient.data._JiaheTempTime });
                    MjClient.native.HelloOC("download file");
    
                },
                downAndPlayVoice: function (msg) {
                    MjClient.native.HelloOC("downloadPlayVoice ok");
                    MjClient.data._tempMessage = msg;
                    MjClient.native.HelloOC(("mas is" + JSON.stringify(msg)));
                    downAndPlayVoice(msg.uid, msg.msg);
                }
            }
        },
        btn_sitdown: {
            _layout:[[0.13,0.11],[0.82,0.10],[0,0]],
            _run:function () { this.visible=true; },
            _click:function () { 
                if ((MjClient.rePlayVideo !== -1)) {
                    return;
                }
                var roomId = MjClient.data.sData.tData.tableid;
                if (roomId > 0) {
                    MjClient.enterGame(roomId, null);
                }
            },
            _event: {
                hideSitdown: function() {
                    this.visible = false;
                },
                initSceneData:function () {
                    var tData = MjClient.data.sData.tData;
                    var uid = SelfUid();
                    if (tData.watchingUids.indexOf(uid) >= 0) {
                        this.visible=true;
                    } else {
                        this.visible=false;
                    }
                }
            }
        },
        block_tuoguan: {
            _layout: [[1, 1], [0.5, 0.5], [0, 0], true], _run: function () {
                this.visible = false;
                this.zIndex = 500;
            }, 
            btn_tuoguan: {
                _touch: function (btn, eT) {
                    if ((eT == 2)) {
                        MjClient.gamenet.request("pkroom.handler.tableMsg", { cmd: "cancelTrust" }, function (rtn) {
                            btn.getParent().setVisible(false);
                        });
                    }
                }
            }, 
            _event: {
                beTrust: function (msg) {
                    if (isLookOn(SelfUid())) {
                        return;
                    }
                    // cc.log(("wxd........beTrust......." + JSON.stringify(msg)));
                    if (getUIPlayer(0) && (getUIPlayer(0).info.uid == msg.uid)) {
                        if (MjClient.movingCard) {
                            MjClient.movingCard.setTouchEnabled(false);
                            MjClient.movingCard.setScale(cardBeginScale);
                            MjClient.movingCard.setTouchEnabled(true);
                        }
                        this.visible = true;
                    }
                },
                cancelTrust: function (msg) {
                    if (getUIPlayer(0) && (getUIPlayer(0).info.uid == msg.uid)) {
                        this.visible = false;
                    }
                },
                initSceneData: function (msg) {
                    if (isLookOn(SelfUid())) {
                        return;
                    }
                    var pl = getUIPlayer(0);
                    if (pl && pl.trust) {
                        this.visible = true;
                    } else {
                        this.visible = false;
                    }
                }
            }
        },
        Panel_vs:{
            _layout:[[1,1],[0.5,0.5],[0,0],true],
            _run:function () { this.visible=false; },
            _event:{
                mjhand:function () { this.visible=false; },
                pkCompareCard:function (msg) {
                    playEffectInPlay("bipai");
                    playEffectInPlay("shangDian");
                    this.visible=true;
                    var _panel_vs = MjClient.playui._Panel_vs;
                    var head1 = _panel_vs.getChildByName("Image_Head1");
                    var p1 = getUIPlayerByUID(msg.uid);
                    if (!p1) {
                        return;
                    }
                    COMMON_UI.refreshHead(this,p1.info.headimgurl,head1);
                    head1.visible = true;
                    var head2 = _panel_vs.getChildByName("Image_Head2");
                    var p2 = getUIPlayerByUID(msg.zhuangUid);
                    if (!p2) {
                        return;
                    }
                    COMMON_UI.refreshHead(this,p2.info.headimgurl,head2);
                    head2.visible = true;

                    var aniNode = _panel_vs.getChildByName("AniNode");
                    comparePKAni_LYC(aniNode);

                    var seq = cc.sequence(
                        cc.delayTime(1),
                        cc.callFunc(function () { 
                            head1.visible = false;
                            head2.visible = false;
                        }),
                        cc.delayTime(1),
                        cc.callFunc(function () { 
                            _panel_vs.visible = false;
                            postEvent("nfTanPai", msg)
                            if (SelfUid() == msg.zhuangUid) {
                                var nBiCount = 0;
                                var sData = MjClient.data.sData;
                                var tData = MjClient.data.sData.tData;
                                for (var id in sData.tData.rungingUids) {
                                    var uid = tData.rungingUids[id];
                                    if (uid != tData.uids[tData.zhuang]) {
                                        var pl = sData.players[uid];
                                        if (pl && !pl.hasBiPai) {
                                            nBiCount++;
                                        }
                                    }
                                }
                                if (nBiCount > 0) {
                                    MjClient.playui.panel_LaoPai.visible = true;
                                } else {
                                    if ((MjClient.rePlayVideo != -1)) {
                                        return;
                                    }
                                    MjClient.gamenet.request("pkroom.handler.tableMsg", { cmd: "pkLaoPai", laopai: false });
                                }
                            }
                        })
                    );
                    _panel_vs.runAction(seq);
                }
            },
            _click:function () { MjClient.playui._Panel_vs.visible=false; }
        },
    },

    ctor: function () {
        this._super();
        var playui = ccs.load("Play_laoyancai.json");
        playMusic("bgFight_common");

        this.srcMaxPlayerNum = MjClient.MaxPlayerNum;
        MjClient.MaxPlayerNum = parseInt(MjClient.data.sData.tData.maxPlayer);
        cc.log("ctor ===", MjClient.MaxPlayerNum)

        this._Node_player1 = playui.node.getChildByName("Node_player1");

        this._Node_Players = [];
        for (var i = 1; i <= MjClient.MaxPlayerNum; i++) {
            var _node_ply = playui.node.getChildByName("Node_player" + i);
            this._Node_Players.push(_node_ply);
        }
       
        MjClient.playui = this;
        MjClient.playui._AniNode = playui.node.getChildByName("eat");
        MjClient.playui._Image_fapai = playui.node.getChildByName("Image_fapai");
        MjClient.playui._Image_banker = playui.node.getChildByName("Image_banker");
        MjClient.playui._Panel_vs = playui.node.getChildByName("Panel_vs");
        MjClient.playui.panel_QiangZhuang1 = this._Node_player1.getChildByName("panel_QiangZhuang1");
        MjClient.playui.panel_BetScore1 = this._Node_player1.getChildByName("panel_BetScore1");
        MjClient.playui._btn_betArr1 = {};
        for (var i = 0; i < 4; i++ ) {
            MjClient.playui._btn_betArr1[i] = MjClient.playui.panel_BetScore1.getChildByName(("btn_bet_" + i));
        }

        MjClient.playui.panel_LaoPai = this._Node_player1.getChildByName("panel_LaoPai");
        MjClient.playui.panel_LaoPai.visible = false;

        MjClient.playui.panel_ZhaPai = this._Node_player1.getChildByName("panel_ZhaPai");
        MjClient.playui.panel_ZhaPai.visible = false;
        
        MjClient.playui._downNode = this._Node_player1;
        BindUiAndLogic(playui.node, this.jsBind);
        this.addChild(playui.node);
        var tData = MjClient.data.sData.tData;
        if ((tData.areaSelectMode.maxPlayer == 3)) {
            this._leftNode.setVisible(false);
        }
        changeMJBg(this, getCurrentMJBgType());
        addClubYaoqingBtn(1);
        
        MjClient.lastMJTick = Date.now();
        this.runAction(cc.repeatForever(cc.sequence(cc.callFunc(function () {
            if (MjClient.game_on_show) {
                MjClient.tickGame(0);
            }

        }), cc.delayTime(7))));
        return true;

    },

    onExit:function()
    {
        this._super();
        MjClient.MaxPlayerNum = this.srcMaxPlayerNum;
    },
});

PlayLayer_laoyancai.prototype.setJiaZhuNum = function (node, pl) {
    if (!(pl)) {
        return;
    }
    var igoldNode = node.getChildByName("head").getChildByName("gold_icon");
    var icountNode = node.getChildByName("head").getChildByName("jiaZhu");
    var icount = pl.jiazhuNum;
    var tData = MjClient.data.sData.tData;
    if ((tData.uids[tData.zhuang] != pl.info.uid) && icountNode && igoldNode && icount > 0) {
        icountNode.visible = true;
        igoldNode.visible = true;
        icountNode.ignoreContentAdaptWithSize(true);
        icountNode.setString(icount);
    }

};

PlayLayer_laoyancai.prototype.getBetScore = function (nBetIdx) {
    var tData = MjClient.data.sData.tData;
    var dizhu = tData.areaSelectMode.diZhu || 1;
    if (([0,1,2,3].indexOf(nBetIdx) == -1)) {
        return dizhu;
    }
    return (nBetIdx + 1) * dizhu;
};

PlayLayer_laoyancai.prototype.showGameRule = function () {
    var tData = MjClient.data.sData.tData;

    var str = ",,,,,";
    str += "坐庄：";
    switch (tData.areaSelectMode.zhuangType) {
        case 0:
            str += "自由抢庄,";
            break;
        case 1:
            str += "房主坐庄,";
            break;
        case 2:
            str += "轮庄模式,";
            break;
        default:
    }
    str = str + "封注：";
    switch (tData.areaSelectMode.fengZhu) {
        case 0:
            str = (str + "5-30,");
            break;
        case 1:
            str = (str + "10-60,");
            break;
        case 2:
            str = (str + "30-80,");
            break;
        case 3:
            str = (str + "50-100,");
            break;
        case 4:
            str = (str + "100-200,");
            break;
        case 5:
            str = (str + "5-10,");
            break;
        default:
    }

    if (tData.areaSelectMode.trustTime > 0) {
        var strTuoguan = "托管类型：";
        switch (tData.areaSelectMode.trustWay) {
            case 0:
                strTuoguan = (strTuoguan + "当局托管,");
                break;
            case 1:
                strTuoguan = (strTuoguan + "当局+下一局,");
                break;
            case 2:
                strTuoguan = (strTuoguan + "整场托管,");
                break;
            default:
                strTuoguan += "无,";
        }
        str = str + strTuoguan;
    }

    var strTuoguan = "托管：";
    if (tData.areaSelectMode.trustTime > 0) {
        strTuoguan += (tData.areaSelectMode.trustTime + "秒后托管,");
    } else {
        strTuoguan += "无托管,";
    }
    str = str + strTuoguan;

    str = (str + (("底分：" + tData.areaSelectMode.diZhu) + "分,"));

    var strPayWay = "支付：";
    switch (tData.areaSelectMode.payWay) {
        case 0:
            strPayWay += "房主付";
            break;
        case 1:
            strPayWay += "AA付";
            break;
        case 2:
            strPayWay += "大赢家付";
            break;
        default:
            strPayWay += "无";
    }
    str = str + strPayWay;

    return (str);

};

PlayLayer_laoyancai.prototype.playChatAni = function (StartOff, EndOff, kind) {
    cc.log("playChatAni ===", StartOff, EndOff, kind)
    var sNode = getNodeLYC(StartOff);
    var eNode = getNodeLYC(EndOff);
    if ((sNode == null) || (eNode == null)) {
        return;
    }
    var StarNode = sNode.getChildByName("head") || sNode.getChildByName("layout_head");
    var EndNode = eNode.getChildByName("head") || eNode.getChildByName("layout_head");
    var startPos = StarNode.convertToWorldSpace(StarNode.getAnchorPointInPoints());
    var endPos = EndNode.convertToWorldSpace(EndNode.getAnchorPointInPoints());
    var _AniNode = MjClient.playui._AniNode;
    var distance = cc.pDistance(startPos, endPos);
    var costTime = (distance / 600);
    if (costTime > 1) {
        costTime = 1;
    } else {
        if (costTime < 0.5) {
            costTime = 0.5;
        }
    }
    var midX = (((endPos.x - startPos.x) / 2) + startPos.x);
    if ((Math.abs((endPos.x - startPos.x)) < 10)) {
        midX = (midX + (distance / 5));
    }
    var midY = Math.max(startPos.y, endPos.y);
    if ((Math.abs((endPos.y - startPos.y)) < 10)) {
        midY = (midY + (distance / 5));
    }
    var move = cc.bezierTo(costTime, [startPos, cc.p(midX, midY), endPos]);
    switch (kind) {
        case 2:
            move = cc.spawn(move, cc.rotateBy(costTime, 720));
            break;
        case 6:
            move = cc.spawn(move, cc.rotateBy(costTime, 360));
            break;
        default:
    }
    cc.spriteFrameCache.addSpriteFrames("playing/other/emj.plist", "playing/other/emj.png");
    var firstFrame = null;
    var sound = "";
    var playSoundFunc = cc.callFunc(function () {
        playEffect(sound);
    });
    switch (kind) {
        case 0:
            sound = "ie_flower";
            firstFrame = new cc.Sprite("playing/other/info_n_send_0.png");
            var frames = [];
            var prefix = "info_n_send_0_";
            var fc = cc.spriteFrameCache;
            for (var i = 1; i < 15; i++) {
                var name = ((prefix + i) + ".png");
                var f = fc.getSpriteFrame(name);
                if (f) {
                    frames.push(f);
                }
            }
            var animate = cc.animate(new cc.Animation(frames, 0.08, 1));
            firstFrame.runAction(cc.sequence(move, playSoundFunc, animate, cc.removeSelf()));
            break;
        case 1:
            sound = "ie_diamond";
            firstFrame = new cc.Sprite("#info_n_send_1_0.png");
            var frames = [];
            var prefix = (("info_n_send_" + kind) + "_");
            var fc = cc.spriteFrameCache;
            for (var i = 1; i < 15; i++) {
                name = ((prefix + i) + ".png");
                f = fc.getSpriteFrame(name);
                if (f) {
                    frames.push(f);
                }
            }
            var animate = cc.animate(new cc.Animation(frames, 0.1, 1));
            firstFrame.runAction(cc.sequence(move, cc.delayTime(0.1), playSoundFunc, animate, cc.removeSelf()));
            break;
        case 2:
            sound = "ie_egg";
            firstFrame = new cc.Sprite("#info_n_send_2_0.png");
            var frames = [];
            var prefix = "info_n_send_2_";
            var fc = cc.spriteFrameCache;
            for (var i = 1; i < 15; i++) {
                name = ((prefix + i) + ".png");
                f = fc.getSpriteFrame(name);
                if (f) {
                    frames.push(f);
                }
            }
            var animate = cc.animate(new cc.Animation(frames, 0.08, 1));
            firstFrame.runAction(cc.sequence(move, playSoundFunc, animate, cc.removeSelf()));
            break;
        case 3:
            sound = "ie_boom";
            firstFrame = new cc.Sprite("#info_n_send_3_0.png");
            var frames = [];
            var prefix = (("info_n_send_" + kind) + "_");
            var fc = cc.spriteFrameCache;
            for (var i = 1; i < 15; i++) {
                name = ((prefix + i) + ".png");
                f = fc.getSpriteFrame(name);
                if (f) {
                    frames.push(f);
                }
            }
            var animate = cc.animate(new cc.Animation(frames, 0.08, 1));
            firstFrame.runAction(cc.sequence(move, cc.delayTime(0.1), playSoundFunc, animate, cc.removeSelf()));
            break;
        case 4:
            sound = "ie_kiss";
            firstFrame = new cc.Sprite("#info_n_send_4_0.png");
            var frames = [];
            var prefix = (("info_n_send_" + kind) + "_");
            var fc = cc.spriteFrameCache;
            for (var i = 1; i < 15; i++) {
                name = ((prefix + i) + ".png");
                f = fc.getSpriteFrame(name);
                if (f) {
                    frames.push(f);
                }
            }
            var animate = cc.animate(new cc.Animation(frames, 0.12, 1));
            firstFrame.runAction(cc.sequence(move, cc.delayTime(0.1), playSoundFunc, animate, cc.removeSelf()));
            break;
        case 5:
            sound = "ie_cheer";
            firstFrame = new cc.Sprite("#info_n_send_5_0.png");
            var frames = [];
            var prefix = (("info_n_send_" + kind) + "_");
            var fc = cc.spriteFrameCache;
            for (var i = 1; i < 15; i++) {
                name = ((prefix + i) + ".png");
                f = fc.getSpriteFrame(name);
                if (f) {
                    frames.push(f);
                }
            }
            var animate = cc.animate(new cc.Animation(frames, 0.12, 1));
            firstFrame.runAction(cc.sequence(move, cc.delayTime(0.1), playSoundFunc, animate, cc.removeSelf()));
            break;
        case 6:
            sound = "ie_tomato";
            firstFrame = new cc.Sprite("#info_n_send_6_0.png");
            var frames = [];
            var prefix = (("info_n_send_" + kind) + "_");
            var fc = cc.spriteFrameCache;
            for (var i = 1; i < 15; i++) {
                name = ((prefix + i) + ".png");
                f = fc.getSpriteFrame(name);
                if (f) {
                    frames.push(f);
                }
            }
            var animate = cc.animate(new cc.Animation(frames, 0.08, 1));
            firstFrame.runAction(cc.sequence(move, playSoundFunc, animate, cc.removeSelf()));
            break;
        default:
    }
    firstFrame.setPosition(startPos);
    firstFrame.setScale((MjClient.size.height / 800));
    _AniNode.addChild(firstFrame, 10000);
};

PlayLayer_laoyancai.prototype.CardLayoutRestore = function(node, off)
{
    return;
};

PlayLayer_laoyancai.prototype.PlayZhaPaiAnimation = function(fromUid)
{
    var sData = MjClient.data.sData;
    var tData = sData.tData;
    var uids = tData.uids;
    var kind = 3;
    var pl = getUIPlayer(0);
    var selfIndex = uids.indexOf(pl.info.uid);
    var playerCount = MjClient.MaxPlayerNum;
    var fromOff = (uids.indexOf(fromUid) - selfIndex + playerCount) % playerCount;
    var zhuangOff = ((tData.zhuang - selfIndex + playerCount) % playerCount);
    if (fromOff == zhuangOff) {
        for (var id in tData.rungingUids) {
            var toUid = tData.rungingUids[id];
            if (toUid != fromUid) {
                var toOff = (uids.indexOf(toUid) - selfIndex + playerCount) % playerCount;
                MjClient.playui.playChatAni(fromOff, toOff, kind);
            }
        }
    } else {
        MjClient.playui.playChatAni(fromOff, zhuangOff, kind);
    }
};

PlayLayer_laoyancai.prototype.playLaoPaiAni = function () {
    // var ani_png = "poker/laoyancai/NewAnimation/NewAnimation0.png";
    // var ani_plist = "poker/laoyancai/NewAnimation/NewAnimation0.plist";
    // var ani_json = "poker/laoyancai/NewAnimation/NewAnimation.ExportJson";
    // ccs.armatureDataManager.addArmatureFileInfo(ani_png, ani_plist, ani_json);
    // var _armature = new ccs.Armature("NewAnimation");
    // _armature.animation.play("Animation1", -1, 0);
    // _armature.setPosition(cc.winSize.width/2, cc.winSize.height/2);
    // _armature.setScale(MjClient.size.width/1280 * 0.8);
    // MjClient.playui.addChild(_armature);

    var _armature = createSpine("poker/laoyancai/NewAnimation/laopaiAni.json", "poker/laoyancai/NewAnimation/laopaiAni.atlas");
    _armature.setAnimation(0, 'animation', false);
    _armature.setPosition(cc.winSize.width/2, cc.winSize.height/2);
    _armature.setScale(MjClient.size.width/1280 * 0.9);
    MjClient.playui.addChild(_armature);

    var seq = cc.sequence(cc.delayTime(1.5),cc.removeSelf());
    _armature.runAction(seq);
};

PlayLayer_laoyancai.prototype.playTurnBankAni = function(msg, callback) {
    var sData = MjClient.data.sData;
    var tData = sData.tData;
    var uids = tData.uids;
    // var selfIndex = uids.indexOf(SelfUid());
    var pl = getUIPlayer(0);
    var selfIndex = uids.indexOf(pl.info.uid);
    var playerCount = MjClient.MaxPlayerNum;
    var turnKuangs = [];
    for (var id in msg.turnUids) {
        var uid = msg.turnUids[id];
        var curIndex = uids.indexOf(uid);
        var curOff = (curIndex - selfIndex + playerCount) % playerCount;
        var curNode = getNodeLYC(curOff);
        var kuang = curNode.getChildByName("head").getChildByName("kuang");
        turnKuangs.push(kuang);
    }

    var img_bank = MjClient.playui._Image_banker;
    img_bank.visible = false;
    var tipCountDown = turnKuangs.length * 5;

    var lastCount = 0;
    img_bank.unscheduleAllCallbacks();
    img_bank.schedule(function () {
        if (tipCountDown >= 0) {
            lastCount = tipCountDown;
            tipCountDown--;
        }

        if (tipCountDown < 0) {
            for (var i=0; i<turnKuangs.length; i++) {
                turnKuangs[i].visible = false;
            }
            img_bank.unscheduleAllCallbacks();
            if (callback) {
                callback();
            }
        } else {
            turnKuangs[lastCount%turnKuangs.length].visible = false;
            turnKuangs[tipCountDown%turnKuangs.length].visible = true;
        }
    }, 0.1, cc.REPEAT_FOREVER, 0);
}

PlayLayer_laoyancai.prototype.playZhuangAni = function (msg) {
    var tData = MjClient.data.sData.tData;
    var uids = tData.uids;
    // var selfIndex = uids.indexOf(SelfUid());
    var pl = getUIPlayer(0);
    var selfIndex = uids.indexOf(pl.info.uid);
    if (selfIndex < 0)
        selfIndex = 0;
    var zhuangOff = (((msg.zhuang + MjClient.MaxPlayerNum) - selfIndex) % MjClient.MaxPlayerNum);
    showZhuangAni_LYC(zhuangOff, function() {
        showUserZhuangLogo_LYC(msg.zhuang, msg.fanbei);
    });
}