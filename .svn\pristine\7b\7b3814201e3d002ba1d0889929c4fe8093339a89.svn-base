function SetUserVisible_LYC(node, off) {
    var pl = getUIPlayer(off);
    var head = node.getChildByName("head");
    var name = head.getChildByName("name");
    var nobody = head.getChildByName("nobody");
    var coin = head.getChildByName("coin");
    var offline = head.getChildByName("offline");
    if (pl) {
        head.visible = true;
        name.visible = true;
        coin.visible = true;
        offline.visible = false;
        coin.visible = true;
        MjClient.loadWxHead(pl.info.uid, pl.info.headimgurl);
        setUserOffline(node, off);
        InitUserHandUI_LYC(node, off);
    } else {
        head.visible = false;
        name.visible = false;
        coin.visible = false;
        offline.visible = false;
        var WxHead = nobody.getChildByName("WxHead");
        if (WxHead) {
            WxHead.removeFromParent(true);
        }
    }
}

function InitUserHandUI_LYC(node, off) {
    var sData = MjClient.data.sData;
    var tData = sData.tData;
    var pl = getUIPlayer(off);
    if (!(pl)) {
        return;
    }
    InitUserCoinAndName(node, off);
    if ((tData.tState == TableState.waitJiazhu) || (tData.tState == TableState.waitCard) || (tData.tState == TableState.roundFinish)) {
        showUserZhuangLogo_LYC(tData.zhuang, tData.zhuangBeishu);
    }

}

// 获取码宝金额（考虑限制）
function getMabaoAmount_LYC(playerOff) {
    var pl = getUIPlayer(playerOff);
    var tData = MjClient.data.sData.tData;

    if (!pl || !pl.lastWinOne || pl.lastWinOne <= 0) {
        return 0;
    }

    // 如果没有设置最大下注限制，返回实际赢分
    if (!tData || !tData.areaSelectMode || !tData.areaSelectMode.maxzhu || tData.areaSelectMode.maxzhu <= 0) {
        return pl.lastWinOne;
    }

    // 如果上局赢分超过最大下注限制，返回限制金额
    if (pl.lastWinOne > tData.areaSelectMode.maxzhu) {
        return tData.areaSelectMode.maxzhu;
    }

    return pl.lastWinOne;
}

// //初始化玩家金币、名字
// function InitUserCoinAndName(node, off){
// 	var pl = getUIPlayer_paohuzi(off);
// 	if(!pl){
// 		return;
// 	}

// 	var tData = MjClient.data.sData.tData;
// 	var bind ={
// 		head:{
// 			name:{
// 				_run: function() {
// 					this.setFontName("Arial");
// 					this.setFontSize(this.getFontSize());
// 				},
// 				_text: function()
// 				{
// 				    var _nameStr = unescape(pl.info.nickname);
// 					return getNewName(_nameStr);
// 				}
// 			},
// 			coin:{
// 				_visible: true,
// 				_run: function(){
// 					//sk,todo,这里有问题，服务器的pl.winall没有赋值，这里加了有个毛用？
// 					var coin = tData.initCoin;
// 					//this.setString("" + coin);
// 					// changeAtalsForLabel(this, coin + pl.winall);
//                     changeAtalsForLabel(this, pl.info.happyBean + (coin + pl.winall));
// 				}
// 			},
// 		}
// 	};
//     //add by sking
//     var name = node.getChildByName("head").getChildByName("name");
//     name.ignoreContentAdaptWithSize(true);

// 	BindUiAndLogic(node, bind);
// }

function InitUserCoinAndName(node, off)
{
    var pl = getUIPlayer(off);
    if(!pl)
    {
        return;
    }


    var tData = MjClient.data.sData.tData;


    //金币场添加金币，金币图标 start
    var showJinBi = MjClient.isInGoldField();
    var jinbiIcon = node.getChildByName("head").getChildByName("jinbiIcon");
    var jinbi = node.getChildByName("head").getChildByName("jinbi");
    if(showJinBi){
        if (!jinbiIcon){
            if(MjClient.getGoldFiledType() == 1){
                jinbiIcon = ccui.ImageView("goldCommon/icon_jinbi.png");
            }else{
                jinbiIcon = ccui.ImageView("playing/gameTable/jinbi.png");
            }
            jinbiIcon.setAnchorPoint(0.5,0.5);
            var coin = node.getChildByName("head").getChildByName("coin");
            jinbiIcon.setPosition(coin.getPositionX()-46, coin.getPositionY());
            jinbiIcon.setName("jinbiIcon");
            node.getChildByName("head").addChild(jinbiIcon);
        }
        if (!jinbi){
            jinbi = new ccui.Text();
            jinbi.setFontSize(20);
            jinbi.setAnchorPoint(0.5,0.5);
            var coin = node.getChildByName("head").getChildByName("coin");
            jinbi.setPosition(coin.getPositionX()+9, coin.getPositionY());
            jinbi.setName("jinbi");
            node.getChildByName("head").addChild(jinbi);
        }
        jinbi.ignoreContentAdaptWithSize(true);
        if (tData.fieldFee){
            if(tData.roundNum <= 0){//结算后台费已经扣了不用再减去台费
                jinbi.setString(MjClient.simplifyGoldNumStr(Number(pl.info.gold)));
            }else{
                jinbi.setString(MjClient.simplifyGoldNumStr(Number(pl.info.gold-tData.fieldFee)));
            }
        }else{
            jinbi.setString(MjClient.simplifyGoldNumStr(pl.info.gold));
        }
        jinbiIcon.setPositionX(jinbi.getPositionX()-jinbi.width/2-jinbiIcon.width/2-10);
    }else{
        if (jinbiIcon){
            node.getChildByName("head").removeChildByName("jinbiIcon")
        }
        if (jinbi){
            node.getChildByName("head").removeChildByName("jinbi")
        }
    }//金币场添加金币，金币图标 end


    var bind =
        {
            head:
                {
                    name:
                        {
                            _run: function() {
                                this.setFontName("Arial");
                                this.setFontSize(this.getFontSize());
                            },
                            _text: function()
                            {
                                var _nameStr = unescape(pl.info.nickname );
                                if(MjClient.getAppType() === MjClient.APP_TYPE.QXHAIANMJ)
                                {
                                    return getPlayerName(_nameStr);
                                }
                                if(MjClient.gameType === MjClient.GAME_TYPE.LV_LIANG_DA_QI || 
                                    MjClient.gameType === MjClient.GAME_TYPE.XIN_ZHOU_SAN_DA_ER || 
                                    MjClient.gameType === MjClient.GAME_TYPE.SHAN_XI_GAN_DENG_YAN)
                                {
                                    return getPlayerName(_nameStr);
                                }
                                return getNewName(_nameStr,5);
                            }
                        },
                    coin:
                        {
                            _visible:function(){
                                if(showJinBi){
                                    return false
                                }
                                
                                if (node.getName() == "down" && MjClient.gameType === MjClient.GAME_TYPE.SHAN_XI_GAN_DENG_YAN)
                                    return tData.tState == TableState.waitJoin || tData.tState == TableState.roundFinish || tData.tState == TableState.waitReady;

                                return true;
                            },
                            _run: function()
                            {
                                //sk,todo,这里有问题，服务器的pl.winall没有赋值，这里加了有个毛用？
                                var coin = tData.initCoin; //tData.initCoin //局内输赢分 ; pl.info.happyBean //本金
                                //this.setString("" + coin);

                                let _coin = Math.floor(pl.info.happyBean + (coin + pl.winall));
                                changeAtalsForLabel(this, _coin);
                                if (MjClient.gameType === MjClient.GAME_TYPE.PAO_DE_KUAI_TY) {
                                     changeAtalsForLabel(this,  coin + pl.winall);
                                }

                            }
                        },
                    money:
                        {
                            _run: function()
                            {
                                this.ignoreContentAdaptWithSize(true);
                            },
                            _visible:function(){
                                return false;
                                if(showJinBi){
                                    return false
                                }
                                
                                if (node.getName() == "down" && MjClient.gameType === MjClient.GAME_TYPE.SHAN_XI_GAN_DENG_YAN)
                                    return tData.tState == TableState.waitJoin || tData.tState == TableState.roundFinish || tData.tState == TableState.waitReady;

                                return true;
                            },
                            _text: function()
                            {
                                //sk,todo,这里有问题，服务器的pl.winall没有赋值，这里加了有个毛用？
                                var coin = pl.info.happyBean;
                                // this.setString("" + coin);
                                return Math.floor(coin + pl.winall);
                            }
                        }
                }
        }

    //add by sking
    var name = node.getChildByName("head").getChildByName("name");
    name.ignoreContentAdaptWithSize(true);
    BindUiAndLogic(node, bind);
}

function resetJiaZhuNum_LYC(node) {
    for (var i = 0; i < MjClient.MaxPlayerNum; i++) {
        MjClient.playui.setJiaZhuNum(getNodeLYC(i),getUIPlayer(i));
    }
}

function getNodeLYC(off) {
    var _node = null;
    if (off >= 0 && off < MjClient.MaxPlayerNum) {
        _node = MjClient.playui._Node_Players[off];
    }
    return _node;
}

function ShowAllCard_LYC(node, off) {
    var pl = getUIPlayer(off);
    if (!(pl)) {
        return;
    }
    if (isCenterJoin(off)) {
        return;
    }
    var CardArr = pl.mjhand.concat();

    cc.log("ShowAllCard_LYC == ", off, JSON.stringify(CardArr))

    for (var i = 0; i < 3; i++) {
        var cardParent = node.getChildByName("Card_" + i);
        cardParent.visible = false;
    }

    for (var i = 0; i < CardArr.length; i++) {
        createNewCard_LYC(node, ("Card_" + i), CardArr[i]);
    }
    showPointCount_LYC(off);
}

function ShowLaoCard_LYC(node, off, card, bSendAni) {
    var sData = MjClient.data.sData;
    var tData = sData.tData;
    var pl = getUIPlayer(off);
    if (!(pl)) {
        return;
    }
    
    if (isCenterJoin(off)) {
        return;
    }

    createLaoCard_LYC(node, "Card_2", card);
}

function createLaoCard_LYC(node, name, tag) {
    cc.log("createLaoCard_LYC ===", name, tag)
    var cardParent = node.getChildByName(name);
    cardParent.tag = tag;
    var textureFile = "poker/card/beimian_lyc.png";
    if ((tag > 0)) {
        var cardType = Math.ceil((tag / 4));
        var flowerType = (tag % 4);
        if ((flowerType == 0)) {
            flowerType = 4;
        }
        if (cardType >= 10) {
            textureFile = (("poker/card/" + flowerType) + cardType) + ".png";
        } else {
            textureFile = (("poker/card/" + flowerType) + ("0" + cardType)) + ".png";
        }
    }
    cardParent.loadTexture(textureFile);
    cardParent.visible = false;
    var tragetPos = cardParent.convertToWorldSpace(cardParent.getAnchorPointInPoints());
    var imgFapai = MjClient.playui._Image_fapai.clone();
    imgFapai.visible = false;
    imgFapai.setPosition(cc.winSize.width/2, cc.winSize.height * 0.8);
    imgFapai.loadTexture(textureFile);
    MjClient.playui.addChild(imgFapai);
    imgFapai.runAction(cc.sequence(
        cc.delayTime(1.5), 
        cc.callFunc(function () {
            imgFapai.visible = true;
            playEffectInPlay("sendCard");
        }),
        cc.delayTime(0.5),
        cc.moveTo(0.5, tragetPos),
        cc.callFunc(function () {
            cardParent.visible = true;
            imgFapai.removeFromParent();
        })));
}

function SendNewCard_LYC() {
    var sData = MjClient.data.sData;
    var tData = sData.tData;
    var uids = tData.uids;
    var selfIndex = uids.indexOf(SelfUid());
    if (selfIndex < 0)
        selfIndex = 0;  //旁观时将-1改为0
    var playerCount = MjClient.MaxPlayerNum;

    var timeTag = 0;
    for (let i = 0; i < playerCount; i++) {
        let curIndex = (tData.zhuang + i) % playerCount;
        let curUid = uids[curIndex];
        if (tData.rungingUids.indexOf(curUid) > -1) {
            let curOff = (uids.indexOf(curUid) - selfIndex + playerCount) % playerCount;
            let offNode = getNodeLYC(curOff);
            let panelCards = offNode.getChildByName("panel_Cards");
            for (let i = 0; i < 3; i++) {
                let cardParent = panelCards.getChildByName("Card_" + i);
                cardParent.visible = false;
            }
            panelCards.runAction(cc.sequence(cc.delayTime((1 * timeTag)), 
                cc.callFunc(function () {
                    ActionTwoCard_LYC(panelCards, curOff);
                })
            ));
            timeTag += 1;
        }
    }
}

function ShowTwoCard_LYC(node, off) {
    let pl = getUIPlayer(off);
    if (!(pl)) {
        return;
    }
    
    if (isCenterJoin(off)) {
        return;
    }

    if (!(pl.mjhand)) {
        pl.mjhand = [0,0];
    }
    let CardArr = pl.mjhand.concat();

    for (let i = 0; i < 3; i++) {
        let cardParent = node.getChildByName("Card_" + i);
        cardParent.visible = false;
    }

    for (let i = 0; i < CardArr.length; i++) {
        let card = CardArr[i];
        createNewCard_LYC(node, ("Card_" + i), card);
    }
}

function ActionTwoCard_LYC(node, off) {
    let pl = getUIPlayer(off);
    if (!(pl)) {
        return;
    }
    
    if (isCenterJoin(off)) {
        return;
    }

    if (!(pl.mjhand)) {
        pl.mjhand = [0,0];
    }
    let CardArr = pl.mjhand.concat();

    for (let i = 0; i < 3; i++) {
        let cardParent = node.getChildByName("Card_" + i);
        cardParent.visible = false;
    }

    for (let i = 0; i < CardArr.length; i++) {
        let card = CardArr[i];
        actionNewCard_LYC(node, i, card);
    }
}

function createNewCard_LYC(node, name, tag) {
    cc.log("createNewCard_LYC ===", name, tag)
    let cardParent = node.getChildByName(name);
    cardParent.visible = true;
    cardParent.tag = tag;
    if ((tag > 0)) {
        let cardType = Math.ceil((tag / 4));
        let flowerType = (tag % 4);
        if ((flowerType == 0)) {
            flowerType = 4;
        }
        cardParent.loadTexture( (cardType >= 10) ? ((("poker/card/" + flowerType) + cardType) + ".png") : ((("poker/card/" + flowerType) + ("0" + cardType)) + ".png"));
    } else {
        cardParent.loadTexture("poker/card/beimian_lyc.png");
    }
}

function actionNewCard_LYC(node, index, tag) {
    cc.log("actionNewCard_LYC ===", index, tag)
    var name = "Card_" + index;
    var cardParent = node.getChildByName(name);
    cardParent.visible = false;
    cardParent.tag = tag;
    if (tag > 0) {
        let cardType = Math.ceil((tag / 4));
        let flowerType = (tag % 4);
        if ((flowerType == 0)) {
            flowerType = 4;
        }
        cardParent.loadTexture( (cardType >= 10) ? ((("poker/card/" + flowerType) + cardType) + ".png") : ((("poker/card/" + flowerType) + ("0" + cardType)) + ".png"));
    } else {
        cardParent.loadTexture("poker/card/beimian_lyc.png");
    }
    var initPos = MjClient.playui._Image_fapai.convertToWorldSpace(MjClient.playui._Image_fapai.getAnchorPointInPoints());
    var tragetPos = cardParent.convertToWorldSpace(cardParent.getAnchorPointInPoints());
    var imgFapai = MjClient.playui._Image_fapai.clone();
    imgFapai.visible = true;
    MjClient.playui._Image_fapai.visible = false;
    var orgScale = imgFapai.getScale();
    imgFapai.setScale(0.01);
    imgFapai.setPosition(initPos);
    imgFapai.loadTexture("poker/card/beimian_lyc.png");
    MjClient.playui.addChild(imgFapai);
    imgFapai.runAction(cc.sequence(cc.delayTime((0.2 * index)), 
    cc.spawn(
        cc.scaleTo(0.6, orgScale),
        cc.moveTo(0.5, tragetPos).easing(cc.easeSineOut()),
        cc.callFunc(function () {
            playEffectInPlay("sendCard");
        })
    ),
    cc.callFunc(function () {
        cardParent.visible = true;
        imgFapai.removeFromParent();
    })));
}

function showPointCount_LYC(off) {
    var pl = getUIPlayer(off);
    var offNode = getNodeLYC(off);
    if ((off == 0)) {
        var play_tips = offNode.getChildByName("play_tip_NN");
    } else {
        play_tips = offNode.getChildByName("head").getChildByName("play_tip_NN");
    }
    if (!(pl)) {
        play_tips.visible = false;
        return ;
    }
    play_tips.visible = true;

    var rate = MjClient.majiang.calRate(pl.handScore);
    var pointNum = MjClient.majiang.handPoint(pl.handScore);
    if (rate != 5) {
        play_tips.loadTexture((("poker/laoyancai/point_" + rate + "_" + pointNum) + ".png"));
        playEffectInPlay("point_" + rate + "_" + pointNum);
    } else {
        play_tips.loadTexture("poker/laoyancai/baozi.png");
        playEffectInPlay("baozi");
    }

    play_tips.ignoreContentAdaptWithSize(true);
    var oldScale = play_tips.getScale();
    play_tips.setScale(0);
    play_tips.runAction(cc.scaleTo(0.5, oldScale).easing(cc.easeBackOut()));

}

function showUserZhuangLogo_LYC(ZhangIdx, fanbei) {
    var tData = MjClient.data.sData.tData;
    if ((tData.areaSelectMode.zhuangType == 1)) {
        return;
    }
    cc.log("showUserZhuangLogo_LYC ===", ZhangIdx, fanbei)
    var uids = tData.uids;
    var selfIndex = uids.indexOf(SelfUid());
    if (selfIndex < 0)
        selfIndex = 0;  //旁观时将-1改为0
    var zhuangOff = (((ZhangIdx + MjClient.MaxPlayerNum) - selfIndex) % MjClient.MaxPlayerNum);
    for (var i = 0; i < MjClient.MaxPlayerNum; i++) {
        var _node = MjClient.playui._Node_Players[i];
        var _zhuang = _node.getChildByName("head").getChildByName("zhuang");
        _zhuang.visible = false;
        var _kuang = _node.getChildByName("head").getChildByName("kuang");
        _kuang.visible = false;
        if (i == zhuangOff) {
            _zhuang.visible = true;
            _kuang.visible = true;
            var _fanbei = _zhuang.getChildByName("fanbei");
            if (_fanbei && fanbei) {
                _fanbei.ignoreContentAdaptWithSize(true);
                _fanbei.setString("x" + fanbei);
            }
        }
    }
}

function showCoins_LYC(startOff, endOff) {
    var StarNode = getNodeLYC(startOff).getChildByName("head").getChildByName("AtlasLabel_Score");
    var EndNode = getNodeLYC(endOff).getChildByName("head").getChildByName("AtlasLabel_Score");
    var startPos = StarNode.convertToWorldSpace(StarNode.getAnchorPointInPoints());
    var endPos = EndNode.convertToWorldSpace(EndNode.getAnchorPointInPoints());
    var distance = cc.pDistance(startPos, endPos);
    var costTime = (distance / 1000);
    if (costTime > 1) {
        costTime = 1;
    } 
    
    if (costTime < 0.5) {
        costTime = 0.5;
    }

    var midX = (((endPos.x - startPos.x) / 2) + startPos.x);
    if ((Math.abs((endPos.x - startPos.x)) < 10)) {
        midX = (midX + (distance / 5));
    }
    var midY = Math.max(startPos.y, endPos.y);
    if ((Math.abs((endPos.y - startPos.y)) < 10)) {
        midY = (midY + (distance / 5));
    }
    var move = cc.bezierTo(costTime, [startPos, cc.p(midX, midY), endPos]);

    for (var i = 0; i < 10; i++) {
        var goldIcon = new cc.Sprite("poker/niuniu/img_gold.png");
        var action = cc.sequence(cc.delayTime(((i * costTime) / 30)), move.clone(), cc.removeSelf());
        goldIcon.runAction(action);
        setWgtLayout(goldIcon, [0.1, 0.1], [0, 0], [0, 0]);
        goldIcon.setPosition(startPos);
        MjClient.playui.addChild(goldIcon, 100);   
    }

    playEffectInPlay("flyMoney");
}

function showScores_LYC(off, score) {
    var tragetNode = getNodeLYC(off).getChildByName("head").getChildByName("AtlasLabel_Score");
    tragetNode.ignoreContentAdaptWithSize(true);
    if ((score < 0)) {
        tragetNode.setProperty(("/" + Math.abs(score)), "poker/common/score_lose.png", 31, 41, ".");
    } else {
        tragetNode.setProperty(("/" + Math.abs(score)), "poker/common/score_win.png", 31, 41, ".");
    }
    tragetNode.setPosition(138.6, 102.5);
    tragetNode.visible = true;
    tragetNode.runAction(cc.sequence(cc.moveBy(1, 0, 40), cc.delayTime(3), cc.callFunc(function () {
        tragetNode.visible = false;

    })));
}

function showZhuangAni_LYC(off, callback) {
    var tragetNode = getNodeLYC(off).getChildByName("head").getChildByName("AtlasLabel_Score");
    var tragetPos = tragetNode.convertToWorldSpace(tragetNode.getAnchorPointInPoints());
    var bankerImg = MjClient.playui._Image_banker;
    setWgtLayout(bankerImg, [0.05, 0.06], [0.5, 0.5], [0, 0]);
    bankerImg.visible = true;
    bankerImg.runAction(cc.sequence(cc.delayTime(0.25), cc.moveTo(0.25, tragetPos), cc.delayTime(0.25), cc.callFunc(function () {
        bankerImg.visible = false;
        if (callback) {
            callback();
        }
    })));
}

function comparePKAni_LYC (ccParentNode) {
    var _armature = createSpine("poker/laoyancai/NewAnimation/pkAini.json", "poker/laoyancai/NewAnimation/pkAini.atlas");
    _armature.setAnimation(0, 'animation', false);
    ccParentNode.addChild(_armature);
    var seq = cc.sequence(cc.delayTime(1.5),cc.removeSelf());
    _armature.runAction(seq);
}