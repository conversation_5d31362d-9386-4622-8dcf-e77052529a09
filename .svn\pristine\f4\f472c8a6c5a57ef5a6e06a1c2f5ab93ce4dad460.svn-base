
var CreateRoomNode_laoyancai = CreateRoomNode.extend({
    initAll: function () {
        this.localStorageKey.KEY_laoyancai_diZhu = "_LAO_YAN_CAI_DI_ZHU";
        this.localStorageKey.KEY_laoyancai_zhuang = "_LAO_YAN_CAI_LUN_ZHUANG";
        this.localStorageKey.KEY_laoyancai_fengzhu = "_LAO_YAN_CAI_FENG_ZHU";
        this.localStorageKey.KEY_laoyancai_tuizhu = "_LAO_YAN_CAI_TUI_ZHU";
        this.localStorageKey.KEY_laoyancai_wanfa = "_LAO_YAN_CAI_WAN_FA";
        this.localStorageKey.KEY_laoyancai_radio = "_LAO_YAN_CAI_RADIO";
        this.localStorageKey.KEY_laoyancai_shunzi = "_LAO_YAN_CAI_SHUN_ZI";
        this.localStorageKey.KEY_laoyancai_wuhua = "_LAO_YAN_CAI_WU_HUA";
        this.localStorageKey.KEY_laoyancai_tonghua = "_LAO_YAN_CAI_TONG_HUA";
        this.localStorageKey.KEY_laoyancai_hunu = "_LAO_YAN_CAI_HU_NU";
        this.localStorageKey.KEY_laoyancai_zadan = "_LAO_YAN_CAI_ZA_DAN";
        this.localStorageKey.KEY_laoyancai_wuxiao = "_LAO_YAN_CAI_WU_XIAO";
        this.localStorageKey.KEY_laoyancai_tonghuashun = "_LAO_YAN_CAI_TONG_HUA_SHUN";
        this.localStorageKey.KEY_laoyancai_yinniu = "_LAO_YAN_CAI_YIN_NIU";
        this.localStorageKey.KEY_laoyancai_yitiaolong = "_LAO_YAN_CAI_YI_TIAO_LONG";
        this.localStorageKey.KEY_laoyancai_tuoguantime = "_LAO_YAN_CAI_TUO_GUAN_TIME";
        this.localStorageKey.KEY_laoyancai_tuoguantype = "_LAO_YAN_CAI_TUO_GUAN_TYPE";
        this.localStorageKey.KEY_laoyancai_difen = "_LAO_YAN_CAI_DI_FEN";
        this.localStorageKey.KEY_laoyancai_cardType = "_LAO_YAN_CAI_CARD_TYPE";
        this.localStorageKey.KEY_laoyancai_laizi = "_LAO_YAN_CAI_LAI_ZI";
        this.localStorageKey.KEY_laoyancai_antiLastJoin = "_LAO_YAN_CAI_ANTI_LAST_JOIN";
        this.localStorageKey.KEY_laoyancai_antiCenterJoin = "_LAO_YAN_CAI_ANTI_CENTER_JOIN";
        this.localStorageKey.KEY_laoyancai_antiCuoPai = "_LAO_YAN_CAI_ANTI_CUO_PAI";
        this.localStorageKey.KEY_laoyancai_startMode = "_LAO_YAN_CAI_START_MODE";
        this.localStorageKey.KEY_laoyancai_antiDissRoom = "_LAO_YAN_CAI_ANTI_DISS_ROOM";
        this.localStorageKey.KEY_laoyancai_mincallfen = "_LAO_YAN_CAI_MIN_CALL_FEN";
        this.localStorageKey.KEY_laoyancai_maxzhu = "_LAO_YAN_CAI_MAX_ZHU";
        this.bgNode = ccs.load("bg_laoyancai.json").node;
        this.addChild(this.bgNode);
        this.bg_node = this.bgNode.getChildByName("bg_laoyancai").getChildByName("view");
        this.bg_node.setScrollBarEnabled(false);

    }, initRoundNode: function () {
        this._super();

    }, initPlayNode: function () {
        var _bgNiuniuNode = this.bg_node;
        var _play = _bgNiuniuNode.getChildByName("play");
        // var fengzhuNodeList = [];
        // fengzhuNodeList.push(_play.getChildByName("cbx_fengzhu_0"));
        // fengzhuNodeList.push(_play.getChildByName("cbx_fengzhu_1"));
        // this._play_fengzhu_radio = createRadioBoxForCheckBoxs(fengzhuNodeList, function (index) {
        //     this.radioBoxSelectCB(index, fengzhuNodeList[index], fengzhuNodeList);
        // }.bind(this));
        // this.addListenerText(fengzhuNodeList, this._play_fengzhu_radio);
        // this.fengzhuNodeList = fengzhuNodeList;

        var zhuangNodeList = [];
        zhuangNodeList.push(_play.getChildByName("zhuang0"));
        zhuangNodeList.push(_play.getChildByName("zhuang1"));
        zhuangNodeList.push(_play.getChildByName("zhuang2"));
        this._zhuang_radio = createRadioBoxForCheckBoxs(zhuangNodeList, function (index) {
            // print(("" + index));
            this.radioBoxSelectCB(index, zhuangNodeList[index], zhuangNodeList);

        }.bind(this));
        this.addListenerText(zhuangNodeList, this._zhuang_radio);
        this.zhuangNodeList = zhuangNodeList;
        
        var that = this;
        this.tuoguantimeCallBack = function (index) {
            that.radioBoxSelectCB(index, tuoguanTimeList[index], tuoguanTimeList);
            // print(("" + index));
            var bShowTuoGuanType = (index != 0);
            _play.getChildByName("tuoguanType_0").visible = bShowTuoGuanType;
            _play.getChildByName("tuoguanType_1").visible = bShowTuoGuanType;
            _play.getChildByName("tuoguanType_2").visible = bShowTuoGuanType;

        };
        var tuoguanTimeList = [];
        tuoguanTimeList.push(_play.getChildByName("tuoguan0"));
        tuoguanTimeList.push(_play.getChildByName("tuoguan1"));
        tuoguanTimeList.push(_play.getChildByName("tuoguan2"));
        tuoguanTimeList.push(_play.getChildByName("tuoguan3"));
        tuoguanTimeList.push(_play.getChildByName("tuoguan4"));
        this._tuoguan_time_radio = createRadioBoxForCheckBoxs(tuoguanTimeList, this.tuoguantimeCallBack);
        this.addListenerText(tuoguanTimeList, this._tuoguan_time_radio, this.tuoguantimeCallBack);
        this.tuoguanTimeList = tuoguanTimeList;
        var tuoguanTypeList = [];
        tuoguanTypeList.push(_play.getChildByName("tuoguanType_0"));
        tuoguanTypeList.push(_play.getChildByName("tuoguanType_1"));
        tuoguanTypeList.push(_play.getChildByName("tuoguanType_2"));
        this._tuoguan_type_radio = createRadioBoxForCheckBoxs(tuoguanTypeList, function (index) {
            this.radioBoxSelectCB(index, tuoguanTypeList[index], tuoguanTypeList);

        }.bind(this));
        this.addListenerText(tuoguanTypeList, this._tuoguan_type_radio);
        this.tuoguanTypeList = tuoguanTypeList;

        var startNodeList = [];
        startNodeList.push(_play.getChildByName("rule_2_start"));
        startNodeList.push(_play.getChildByName("rule_3_start"));
        startNodeList.push(_play.getChildByName("rule_4_start"));
        this._startNode_radio = createRadioBoxForCheckBoxs(startNodeList, function (index) {
            this.radioBoxSelectCB(index, startNodeList[index], startNodeList);
        }.bind(this));
        this.addListenerText(startNodeList, this._startNode_radio);
        this.startNodeList = startNodeList;

        var img_mincall = _play.getChildByName("img_mincall");
        this.edit_mincall = CreateRoomNode.initDifenEditBox(img_mincall);

        var img_maxzhu = _play.getChildByName("img_maxzhu");
        this.edit_maxzhu = CreateRoomNode.initDifenEditBox(img_maxzhu);

        var ccDiFenParent = this.bgNode.getChildByName("bg_laoyancai");
        this._zhuIdx = 1;
        this._ZhuNum = ccDiFenParent.getChildByName("txt_fen");
        if (this._ZhuNum) {
            this._ZhuNum.setString(this._zhuIdx);
            this._Button_sub = ccDiFenParent.getChildByName("btn_sub");
            this._Button_sub.addTouchEventListener(function (sender, type) {
                if ((type == 2)) {
                    if ((this._zhuIdx <= 0.1)) {
                        this._zhuIdx = 11;
                    }
                    if ((this._zhuIdx > 0)) {
                        var step = 0.1;
                        if ((this._zhuIdx > 1)) {
                            step = 1;
                        } else if ((this._zhuIdx > 0.5)) {
                            step = 0.5;
                        }
                        this._zhuIdx = (this._zhuIdx - step);
                        this._zhuIdx = correctAccuracy(this._zhuIdx, 5);
                        this._ZhuNum.setString(this._zhuIdx);
                        this._Button_add.setTouchEnabled(true);
                        this._Button_add.setBright(true);
                        this.setRoomCardModeFree();
                    }
                }

            }, this);
            this._Button_add = ccDiFenParent.getChildByName("btn_add");
            this._Button_add.addTouchEventListener(function (sender, type) {
                if ((this._zhuIdx == 10)) {
                    this._zhuIdx = 0;
                }
                if ((this._zhuIdx < 10)) {
                    var step = 0.1;
                    if ((this._zhuIdx >= 1)) {
                        step = 1;
                    } else if ((this._zhuIdx >= 0.5)) {
                        step = 0.5;
                    }
                    this._zhuIdx = (this._zhuIdx + step);
                    this._zhuIdx = correctAccuracy(this._zhuIdx, 5);
                    this._ZhuNum.setString(this._zhuIdx);
                    this._Button_sub.setTouchEnabled(true);
                    this._Button_sub.setBright(true);
                    this.setRoomCardModeFree();
                }

            }, this);
        }
        this._ZhuNum.visible = false;
        this._Button_add.visible = false;
        this._Button_sub.visible = false;
        var _img_3 = ccDiFenParent.getChildByName("Image_3");
        this.edit_diFen = CreateRoomNode.initDifenEditBox(_img_3);

    },
    setPlayNodeCurrentSelect: function (isClub) {
        var nZhuangIdx = 0;
        if (isClub) {
            nZhuangIdx = this.getNumberItem("zhuangType", 0);
        } else {
            nZhuangIdx = util.localStorageEncrypt.getNumberItem(this.localStorageKey.KEY_laoyancai_zhuang, 0);
        }
        this._zhuang_radio.selectItem(nZhuangIdx);
        this.radioBoxSelectCB(nZhuangIdx, this.zhuangNodeList[nZhuangIdx], this.zhuangNodeList);
        // var nFengZhuIdx;
        // if (isClub) {
        //     nFengZhuIdx = this.getNumberItem("fengZhu", 0);
        // } else {
        //     nFengZhuIdx = util.localStorageEncrypt.getNumberItem(this.localStorageKey.KEY_laoyancai_fengzhu, 0);
        // }
        // this._play_fengzhu_radio.selectItem(nFengZhuIdx);
        // this.radioBoxSelectCB(nFengZhuIdx, this.fengzhuNodeList[nFengZhuIdx], this.fengzhuNodeList);

        var _trustTime;
        if (isClub) {
            _trustTime = [0, 15, 30, 45, 60].indexOf(this.getNumberItem("trustTime", 1));
        } else {
            _trustTime = util.localStorageEncrypt.getNumberItem(this.localStorageKey.KEY_laoyancai_tuoguantime, 1);
        }
        this._tuoguan_time_radio.selectItem(_trustTime);
        this.radioBoxSelectCB(_trustTime, this.tuoguanTimeList[_trustTime], this.tuoguanTimeList);
        this.tuoguantimeCallBack(_trustTime);

        var _trustType;
        if (isClub) {
            _trustType = this.getNumberItem("trustWay", 0);
        } else {
            _trustType = util.localStorageEncrypt.getNumberItem(this.localStorageKey.KEY_laoyancai_tuoguantype, 0);
        }
        this._tuoguan_type_radio.selectItem(_trustType);
        this.radioBoxSelectCB(_trustType, this.tuoguanTypeList[_trustType], this.tuoguanTypeList);

        var nStartModeIdx;
        if (isClub) {
            nStartModeIdx = this.getNumberItem("startmode", 0);
        } else {
            nStartModeIdx = util.localStorageEncrypt.getNumberItem(this.localStorageKey.KEY_laoyancai_startMode, 0);
        }
        this._startNode_radio.selectItem(nStartModeIdx);
        this.radioBoxSelectCB(nStartModeIdx, this.startNodeList[nStartModeIdx], this._startNode_radio);

        var diFen = 1;
        if (isClub){
            diFen = this.getNumberItem("difen", 1);
        }else {
            diFen = util.localStorageEncrypt.getNumberItem(this.localStorageKey.KEY_laoyancai_difen, 1);
        }
        if (this.edit_diFen) {
            this.edit_diFen.setString(diFen + "");
        }

        var mincallFen = 0;
        if (isClub){
            mincallFen = this.getNumberItem("minCallBankLimit", 0);
        }else {
            mincallFen = util.localStorageEncrypt.getNumberItem(this.localStorageKey.KEY_laoyancai_mincallfen, 0);
        }
        if (this.edit_mincall) {
            this.edit_mincall.setString(mincallFen + "");
        }

        var maxzhu = 0;
        if (isClub){
            maxzhu = this.getNumberItem("maxzhu", 0);
        }else {
            maxzhu = util.localStorageEncrypt.getNumberItem(this.localStorageKey.KEY_laoyancai_maxzhu, 0);
        }
        if (this.edit_maxzhu) {
            this.edit_maxzhu.setString(maxzhu + "");
        }
    },

    // initSpecialRule: function (index) {
    //     if (index == 0) {
    //         this.rule5_0.getChildByName("text").setString("顺子牛x5")
    //         this.rule5_1.getChildByName("text").setString("金牛x5")
    //         this.rule5_2.getChildByName("text").setString("同花牛x5")
    //         this.rule5_3.getChildByName("text").setString("葫芦牛x6")
    //         this.rule5_4.getChildByName("text").setString("炸弹牛x6")
    //         this.rule5_5.getChildByName("text").setString("五小牛x8")
    //         this.rule5_6.getChildByName("text").setString("同花顺x10")
    //         this.rule5_7.getChildByName("text").setString("银牛x5")
    //         this.rule5_7.setPosition(this.pos5_1)
    //         this.rule5_1.setPosition(this.pos5_2)
    //         this.rule5_2.setPosition(this.pos5_3)
    //         this.rule5_3.setPosition(this.pos5_4)
    //         this.rule5_4.setPosition(this.pos5_5)
    //         this.rule5_5.setPosition(this.pos5_6)
    //         this.rule5_6.setPosition(this.pos5_7)
    //         this.rule5_8.visible = false
    //         this.rule5_1.visible = true
    //         this.rule5_7.visible = true
    //         this._zhuang = 10
    //     } else if (index == 1) {
    //         this.rule5_0.getChildByName("text").setString("顺子牛x15")
    //         this.rule5_2.getChildByName("text").setString("同花牛x20")
    //         this.rule5_3.getChildByName("text").setString("葫芦牛x25")
    //         this.rule5_4.getChildByName("text").setString("炸弹牛x30")
    //         this.rule5_5.getChildByName("text").setString("五小牛x35")
    //         this.rule5_6.getChildByName("text").setString("同花顺x40")
    //         this.rule5_8.getChildByName("text").setString("一条龙x35")
    //         this.rule5_2.setPosition(this.pos5_1)
    //         this.rule5_3.setPosition(this.pos5_2)
    //         this.rule5_4.setPosition(this.pos5_3)
    //         this.rule5_5.setPosition(this.pos5_4)
    //         this.rule5_8.setPosition(this.pos5_5)
    //         this.rule5_6.setPosition(this.pos5_6)
    //         this.rule5_8.visible = true
    //         this.rule5_1.visible = false
    //         this.rule5_7.visible = false
    //         this._zhuang = 10
    //     }
    // },
    getSelectedPara: function () {
        var para = {};
        para.gameType = MjClient.GAME_TYPE.LAO_YAN_CAI;
        para.difen = parseFloat(this.edit_diFen.getString()) || 1;
        para.maxPlayer = 7;
        para.fengZhu = 0;
        // var nZhuangIndex = this._zhuang_radio.getSelectIndex();
        para.zhuangType = 0;
        para.diZhu = para.difen;
        para.tuiZhuTimes = 0;
        para.minCallBankLimit = parseInt(this.edit_mincall.getString()) || 0;
        para.maxzhu = parseInt(this.edit_maxzhu.getString()) || 0;
        para.startmode = this._startNode_radio.getSelectIndex();
        var nTuoguanIdx = this._tuoguan_time_radio.getSelectIndex();
        para.trustTime = [0, 15, 30, 45, 60][nTuoguanIdx];
        para.trustWay = this._tuoguan_type_radio.getSelectIndex();
        // para.tongPaiXingDaXiao = this._player_cardType_radio.getSelectIndex();
        if (!this._isFriendCard) {
            util.localStorageEncrypt.setNumberItem(this.localStorageKey.KEY_laoyancai_zhuang, para.zhuangType);
            util.localStorageEncrypt.setNumberItem(this.localStorageKey.KEY_laoyancai_tuoguantime, nTuoguanIdx);
            util.localStorageEncrypt.setNumberItem(this.localStorageKey.KEY_laoyancai_tuoguantype, para.trustWay);
            util.localStorageEncrypt.setNumberItem(this.localStorageKey.KEY_laoyancai_fengzhu, para.fengZhu);
            // util.localStorageEncrypt.setNumberItem(this.localStorageKey.KEY_laoyancai_tuizhu, para.tuiZhuTimes);
            util.localStorageEncrypt.setNumberItem(this.localStorageKey.KEY_laoyancai_diZhu, para.diZhu);
            // util.localStorageEncrypt.setNumberItem(this.localStorageKey.KEY_laoyancai_radio, para.niuTypeTimes);
            util.localStorageEncrypt.setNumberItem(this.localStorageKey.KEY_laoyancai_difen, para.difen);
            // util.localStorageEncrypt.setNumberItem(this.localStorageKey.KEY_laoyancai_wanfa, para.wuHua);
            // util.localStorageEncrypt.setNumberItem(this.localStorageKey.KEY_laoyancai_shunzi, para.shunZiNiu);
            // util.localStorageEncrypt.setNumberItem(this.localStorageKey.KEY_laoyancai_wuhua, para.jinNiu);
            // util.localStorageEncrypt.setNumberItem(this.localStorageKey.KEY_laoyancai_tonghua, para.tongHuaNiu);
            // util.localStorageEncrypt.setNumberItem(this.localStorageKey.KEY_laoyancai_hunu, para.huLuNiu);
            // util.localStorageEncrypt.setNumberItem(this.localStorageKey.KEY_laoyancai_zadan, para.zhaDanNiu);
            // util.localStorageEncrypt.setNumberItem(this.localStorageKey.KEY_laoyancai_wuxiao, para.wuXiaoNiu);
            // util.localStorageEncrypt.setNumberItem(this.localStorageKey.KEY_laoyancai_yinniu, para.yinNiu);
            // util.localStorageEncrypt.setNumberItem(this.localStorageKey.KEY_laoyancai_yitiaolong, para.yiTiaoLong);
            // util.localStorageEncrypt.setNumberItem(this.localStorageKey.KEY_laoyancai_tonghuashun, para.tongHuaShunNiu);
            // util.localStorageEncrypt.setNumberItem(this.localStorageKey.KEY_laoyancai_cardType, para.tongPaiXingDaXiao);
            // util.localStorageEncrypt.setBoolItem(this.localStorageKey.KEY_laoyancai_antiLastJoin, para.antiLastJoin);
            // util.localStorageEncrypt.setBoolItem(this.localStorageKey.KEY_laoyancai_antiCenterJoin, para.antiCenterJoin);
            // util.localStorageEncrypt.setBoolItem(this.localStorageKey.KEY_laoyancai_antiDissRoom, para.antiDissRoom);
            // util.localStorageEncrypt.setBoolItem(this.localStorageKey.KEY_laoyancai_antiCuoPai, para.antiCuoPai);
            util.localStorageEncrypt.setNumberItem(this.localStorageKey.KEY_laoyancai_startMode, para.startmode);
            util.localStorageEncrypt.setNumberItem(this.localStorageKey.KEY_laoyancai_maxzhu, para.maxzhu);
        }
        cc.log("" + JSON.stringify(para));
        return para;
    },

    getBetScore: function (fengZhu) {
        switch (fengZhu) {
            case 0:
                return 2;
            case 1:
                return 4;
            default:
                return 1;
        }
    }
});