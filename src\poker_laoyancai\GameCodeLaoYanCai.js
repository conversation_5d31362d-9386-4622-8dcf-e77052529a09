function GameCodeLaoYanCai(majiang, app)
{
    this.majiang = majiang;
    this.app = app;
    this.timerId = null;
    this.autoLaoPai = 0;  //自动捞牌人数
}

GameCodeLaoYanCai.prototype.initPlayer = function (pl)
{
    pl.winone = 0;
    pl.mabao = 0;
    pl.hasBiPai = false; //闲家是否已经比过牌
    pl.biPaiResult = 0;  //比牌结果(-1:输了，0：平局，1：赢了)
};

GameCodeLaoYanCai.prototype.doBeforeHands = function(tb){
    // var tData = tb.tData;
    // for (var i = 0; i < tData.rungingUids.length; i++)
    // {
    //     var p = tb.players[tData.rungingUids[i]];
    //     if (!p.trustWholeTimes || p.trustWholeTimes <= 0) {
    //         p.trustWholeTimes = [1,2,100][tb.tData.areaSelectMode.trustWay || 0];
    //     }
    // }
    this.autoLaoPai = 0;
};

GameCodeLaoYanCai.prototype.initAreaSelectMode = function(tb)
{
    var tData = tb.tData;
    tData.areaSelectMode["difen"] = tb.createParams.difen || 1;
    tData.areaSelectMode["diZhu"] = tb.createParams.diZhu || 1;
    tData.areaSelectMode["zhuangType"] = tb.createParams.zhuangType || 0;                //0:自由抢庄(明牌倍数抢庄);1:固定庄家;2:轮流上庄
    tData.areaSelectMode["maxPlayer"] = tb.createParams.maxPlayer;
    tData.areaSelectMode["gameType"] = tb.createParams.gameType;

    tData.areaSelectMode["minCallBankLimit"] = tb.createParams.minCallBankLimit;    //最小抢庄欢乐豆

    tData.areaSelectMode["tongPaiXingDaXiao"] = tb.createParams.tongPaiXingDaXiao || 1;  //同牌型大小判断

    tData.areaSelectMode["trustTime"] = tb.createParams.trustTime;
    tData.areaSelectMode["trustWay"] = tb.createParams.trustWay;
    tData.areaSelectMode["fengZhu"] = tb.createParams.fengZhu;
    tData.areaSelectMode["startmode"] = tb.createParams.startmode;
    
    tData.gameCnName = "捞腌菜";
};

// 开始人数
GameCodeLaoYanCai.prototype.StartPlayerCount = function(tb)
{
    if(tb.createParams.startmode == 0){
        return 2;
    }
    else if(tb.createParams.startmode == 1){
        return 3;
    }
    else if(tb.createParams.startmode == 2){
        return 4;
    }
};

// 是否需要手动准备
GameCodeLaoYanCai.prototype.isBtnReady = function(tb)
{
    return true;
};

//开始房主坐庄
GameCodeLaoYanCai.prototype.startOwnerBank = function (tData, tb)
{
    var tData = tb.tData;
    tData.trustEnd = -1;

    //确定庄家
    var zp = null;
    tData.zhuang = 0;
    zp = tb.getPlayer(tData.uids[tData.zhuang]);

    tData.tState = TableState.waitJiazhu;
    for (var i = 0; i < tData.rungingUids.length; i++)
    {
        var p = tb.players[tData.rungingUids[i]];
        p.mjState = TableState.waitJiazhu;
    }

    var msg = {};
    msg.zhuang = tData.zhuang;
    msg.rungingUids = JSON.parse(JSON.stringify(tData.rungingUids));
    tb.NotifyAll("waitJiazhu", msg);
    cloneDataAndPush(tb.mjlog, "waitJiazhu", msg);

    //加注定时器
    this.waitJiaZhu(tb);
};

//开始轮庄
GameCodeLaoYanCai.prototype.startTrunBank = function (tData, tb)
{
    var tData = tb.tData;
    tData.trustEnd = -1;

    //确定庄家
    var zp = null;
    if(tb.createParams.clubId)
    {
        var randIdx  = (tData.zhuang + 1) % tData.rungingUids.length;
        var tmpZpUid  = tData.rungingUids[randIdx];
        for(var i = 0; i < tData.uids.length;i++)
        {
            if(tData.uids[i] == tmpZpUid)
            {
                tData.zhuang = i;
                break;
            }
        }
    }
    else
    {
        tData.zhuang = (tData.zhuang + 1) % tData.rungingUids.length;
    }
    zp = tb.getPlayer(tData.uids[tData.zhuang]);

    tData.tState = TableState.waitJiazhu;
    for (var i = 0; i < tData.rungingUids.length; i++)
    {
        var p = tb.players[tData.rungingUids[i]];
        p.mjState = TableState.waitJiazhu;
    }

    var msg = {};
    msg.zhuang = tData.zhuang;
    msg.rungingUids = JSON.parse(JSON.stringify(tData.rungingUids));
    tb.NotifyAll("waitJiazhu", msg);
    cloneDataAndPush(tb.mjlog, "waitJiazhu", msg);

    //加注定时器
    this.waitJiaZhu(tb);
};

//开始抢庄(抢与不抢)自由抢庄玩法
GameCodeLaoYanCai.prototype.startCallBank = function (tData, tb)
{
    var tData = tb.tData;
    tData.trustEnd = -1;
    tData.tState = TableState.waitCallBank;
    for (var i = 0; i < tData.rungingUids.length; i++)
    {
        var p = tb.players[tData.rungingUids[i]];
        p.mjState = TableState.waitCallBank;
    }

    tb.NotifyAll("startCallBank", {rungingUids:tData.rungingUids});
    cloneDataAndPush(tb.mjlog, "startCallBank", {rungingUids:tData.rungingUids});

    //波波：抢庄定时器
    this.waitCallBank(tb);
};

//明牌抢庄玩法
GameCodeLaoYanCai.prototype.startMingCallBank = function (tData, tb)
{
    var tData = tb.tData;
    logger.debug("++++ 明牌抢庄玩法 tb.createParams = " + JSON.stringify(tb.createParams));

    //开始抢庄逻辑
    tData.tState = TableState.waitCallBank;
    var maxPlayer = tData.rungingUids.length;
    for (var i = 0; i < maxPlayer; i++)
    {
        var p = tb.players[tData.rungingUids[i]];
        p.mjState = TableState.waitCallBank;
    }

    tb.NotifyAll("startCallBank", {rungingUids:tData.rungingUids});
    cloneDataAndPush(tb.mjlog, "startCallBank", {rungingUids:tData.rungingUids});

    //抢庄定时器
    this.waitCallBank(tb);
};

//抢庄定时器
GameCodeLaoYanCai.prototype.waitCallBank = function(tb)
{
    if (tb.nntimerId) {
        clearTimeout(tb.nntimerId);
        tb.nntimerId = null;
    }

    var tData = tb.tData;
    if(tData.areaSelectMode.trustTime <= 0) return;

    var countdown = tData.areaSelectMode.trustTime;
    tb.NotifyAll("trustTip", {tipCountDown: countdown});
    cloneDataAndPush(tb.mjlog, "trustTip", {tipCountDown: countdown});

    var _this = this;
    tb.nntimerId = setTimeout(() =>
    {
        if (tb.nntimerId) {
            clearTimeout(tb.nntimerId);
            tb.nntimerId = null;
        }

        var maxPlayer = tData.rungingUids.length;
        for (var i = 0; i < maxPlayer; i++)
        {
            var pl = tb.players[tData.rungingUids[i]];
            if(tData.tState == TableState.waitCallBank && pl.mjState == TableState.waitCallBank)
            {
                var msg = {};
                msg.callBankNum = 0;
                pl.callBankNum = Number(msg.callBankNum);
                _this.NNCallBank(true, pl, msg, null, null, tb);
            }
        }
    }, tData.areaSelectMode.trustTime * 1000);
};

//客户端抢庄动作（0-4倍）
GameCodeLaoYanCai.prototype.NNCallBank = function(bTrust, pl, msg, session, next, tb)
{
    //数据效验
    logger.debug("NNCallBank player=%d msg.callBankNum=%d", pl.uid, msg.callBankNum);
    if (msg.callBankNum > 4) {
        logger.error("error NNCallBank player=%d msg.callBankNum=%d", pl.uid, msg.callBankNum);
        return;
    }

    var tData = tb.tData;

    if(tData.tState == TableState.waitCallBank &&  pl.mjState == TableState.waitCallBank)
    {
        pl.mjState = TableState.isReady;

        //俱乐部才有
        var minCallBankLimit = 0;
        var reason = 0;
        pl.callBankNum = Number(msg.callBankNum);
        //波波：保存叫庄倍数，掉线重连需要
       
        if(tData.areaSelectMode["minCallBankLimit"] && tb.createParams.clubId)
        {
            minCallBankLimit = Number(tData.areaSelectMode["minCallBankLimit"]);
            var leftHappyBean = pl.info.happyBean + pl.winall;
            if(pl.info.happyBean != null && leftHappyBean < minCallBankLimit && pl.callBankNum > 0)
            {
                pl.callBankNum = 0;
                reason = 1;     //分数不足，无法抢庄
            }
        }

        tb.NotifyAll("NNCallBank", {callBankNum : pl.callBankNum, uid : pl.uid ,errReason :reason});
        cloneDataAndPush(tb.mjlog, "NNCallBank", {callBankNum : pl.callBankNum, uid : pl.uid, errReason :reason});

        var maxPlayer = tData.rungingUids.length;
        var bAllReady = true;
        for (var i = 0; i < maxPlayer; i++)
        {
            var p = tb.players[tData.rungingUids[i]];
            if(p.mjState != TableState.isReady)
            {
                bAllReady = false;
                break;
            }
        }

        //抢庄完毕
        if(bAllReady)
        {
            logger.debug("抢庄完毕！！！！ ");

            var calBankMaxUserCount = 0;
            var callBankUser = [];
            var maxCallBankNum = Number(msg.callBankNum);
            var maxCallNumUid = [];
            for (var i = 0; i < tData.rungingUids.length; i++)
            {
                var p = tb.players[tData.rungingUids[i]];
                if(p.callBankNum > maxCallBankNum)
                {
                    maxCallBankNum = p.callBankNum;
                }
            }

            //记录最大倍数抢庄玩家的uid
            for (var i = 0; i < tData.rungingUids.length; i++)
            {
                var p = tb.players[tData.rungingUids[i]];
                if(p.callBankNum == maxCallBankNum)
                {
                    maxCallNumUid.push(p.uid);
                }
            }

            for (var i = 0; i < tData.rungingUids.length; i++)
            {
                var p = tb.players[tData.rungingUids[i]];
                if(p.callBankNum == maxCallBankNum)
                {
                    calBankMaxUserCount++;
                    callBankUser.push(p.uid);
                }
            }
    
            //都不抢，随机一个
            if (calBankMaxUserCount == 0)
            {
                tData.zhuang = Math.floor(Math.random() * tData.rungingUids.length);
            }
            else
            {
                //客户端庄的位置是uids相对位子，而不是rungingUids的位子
                var randIdx = Math.floor(Math.random() * calBankMaxUserCount);
                //tData.zhuang = tData.rungingUids.indexOf(callBankUser[randIdx]);
                var zhuangUid = callBankUser[randIdx];
                for(var i = 0; i < tData.uids.length; i++)
                {
                    if(zhuangUid == tData.uids[i])
                    {
                        tData.zhuang = i;
                        break;
                    }
                }
            }

            tData.zhuangBeishu = maxCallBankNum ? maxCallBankNum : 1;

            //播放定庄动画
            var turnBankMsg = {};
            turnBankMsg.zhuang = tData.zhuang;
            turnBankMsg.fanbei = tData.zhuangBeishu;
            turnBankMsg.turnUids = maxCallNumUid;
            tb.NotifyAll("playTurnBankAnnimation", turnBankMsg);
            cloneDataAndPush(tb.mjlog, "playTurnBankAnnimation", turnBankMsg);

            var maxCallNums = maxCallNumUid.length + 1;
            var _this = this;
            var timeId = null;
            timeId = setTimeout(() => {
                clearTimeout(timeId);
                _this.startJiaZhu(tb);
            }, maxCallNums * 600);
        }
    }  
};

//开始下注
GameCodeLaoYanCai.prototype.startJiaZhu = function(tb)
{
    var tData = tb.tData;
    tData.tState = TableState.waitJiazhu;
    var mabaoUids = [];
    for (var i = 0; i < tData.rungingUids.length; i++)
    {
        var p = tb.players[tData.rungingUids[i]];
        if (tData.rungingUids.indexOf(p.uid) < 0)  break;
        p.mjState = TableState.waitJiazhu;
        if (p.lastWinOne > 0) {
            mabaoUids.push(p.uid);
        }
    }

    var jiaZhuMsg = {};
    jiaZhuMsg.zhuang = tData.zhuang;
    jiaZhuMsg.fanbei = tData.zhuangBeishu;
    jiaZhuMsg.rungingUids =  JSON.parse(JSON.stringify(tData.rungingUids));
    jiaZhuMsg.mabaoUids = mabaoUids;

    tb.NotifyAll("waitJiazhu", jiaZhuMsg);
    cloneDataAndPush(tb.mjlog, "waitJiazhu", jiaZhuMsg);
    this.waitJiaZhu(tb);
};

//等待下注定时器
GameCodeLaoYanCai.prototype.waitJiaZhu = function(tb)
{
    if (tb.nntimerId) {
        clearTimeout(tb.nntimerId);
        tb.nntimerId = null;
    }

    var tData = tb.tData;
    if(tData.areaSelectMode.trustTime <= 0) return;

    var maxPlayer = tData.rungingUids.length;
    var zp = tb.getPlayer(tData.uids[tData.zhuang]);
    var countdown = tData.areaSelectMode.trustTime;
    tb.NotifyAll("trustTip", {tipCountDown: countdown});
    cloneDataAndPush(tb.mjlog, "trustTip", {tipCountDown: countdown});

    var _this = this;
    tb.nntimerId = setTimeout(() =>
    {
        if (tb.nntimerId) {
            clearTimeout(tb.nntimerId);
            tb.nntimerId = null;
        }

        for (var i = 0; i < maxPlayer; i++)
        {
            var pl = tb.players[tData.rungingUids[i]];
            if(tData.tState == TableState.waitJiazhu && pl.mjState == TableState.waitJiazhu)
            {
                if(pl != zp)
                {
                    var msg = {};
                    msg.jiazhuNum = tb.createParams.diZhu;
                    pl.jiazhuNum = parseInt(msg.jiazhuNum); 
                    pl.trustTimer = null;
                    _this._mjJiazhu(pl, msg, null, null, tb);  
                }
            }
        }
    }, tData.areaSelectMode.trustTime * 1000);
};

//客户端加注动作（目前最大加注不能超过4倍底注，码宝要除外）
GameCodeLaoYanCai.prototype.mjJiazhuNN = function (bTrust, pl, msg, session, next, tb)
{
    var tData = tb.tData;

    //数据效验
    logger.debug("mjJiazhuNN player=%d msg.jiazhuNum=%d", pl.uid, msg.jiazhuNum);
    if (msg.jiazhuNum < 0 || msg.jiazhuNum > tData.areaSelectMode.dizhu * 4) {
        logger.debug("error player=%d msg.jiazhuNum=%d", pl.uid, msg.jiazhuNum);
        return;
    }

    this._mjJiazhu(pl, msg, session, next, tb);
}

GameCodeLaoYanCai.prototype._mjJiazhu = function (pl, msg, session, next, tb)
{
    var tData = tb.tData;
    
    if(tData.tState == TableState.waitJiazhu &&  pl.mjState == TableState.waitJiazhu)
    {
        pl.mjState = TableState.isReady;

        pl.jiazhuNum = parseInt(msg.jiazhuNum);

        var jiaZhuMsg = {};
        jiaZhuMsg.jiazhuNum = pl.jiazhuNum,
        jiaZhuMsg.uid =  pl.uid,
        tb.NotifyAll('MJJiazhu', jiaZhuMsg);
        cloneDataAndPush(tb.mjlog, 'MJJiazhu', jiaZhuMsg);

        var zp = tb.getPlayer(tData.uids[tData.zhuang]);
        if (zp.mjState == TableState.waitJiazhu)
        {
            zp.mjState = TableState.isReady;
        }
        var maxPlayer = tData.rungingUids.length;
        var bAllJiaZhu = true;
        for (var i = 0; i < maxPlayer; i++)
        {
            var p = tb.players[tData.rungingUids[i]];
            if(p.mjState != TableState.isReady)
            {
                bAllJiaZhu = false;
                break;
            }
        }

        if(bAllJiaZhu)
        {
            if (tb.nntimerId) {
                clearTimeout(tb.nntimerId);
                tb.nntimerId = null;
            }

            //下注阶段如果在玩的闲家下注玩。超级注意：下面这行获取庄家错误，导致熬夜通宵三个，他妈的江玉
            ////var zp = tb.getPlayer(tData.rungingUids[tData.zhuang]);

            //游戏开始发牌
            tb.runStartGameLYC();

            //延时3秒再操作
            var _this = this;
            var timeId = null;
            var dalay = Math.max(3, maxPlayer);
            timeId = setTimeout(() => {
                clearTimeout(timeId);
                _this.beginOperation(tb);
            }, 1000*dalay);
        }
    }
};

//开始操作
GameCodeLaoYanCai.prototype.beginOperation = function(tb) {
    var tData = tb.tData;
    var zp = tb.getPlayer(tData.uids[tData.zhuang]);
    if (zp) {
        var zpHandScore = this.majiang.calHandScore(zp.mjhand);
        if (zpHandScore >= 8) {
            //提示庄家炸牌
            tData.tState = TableState.waitZhaPai;
            zp.mjState = TableState.waitZhaPai;
            zp.notify("nfZhaPai", {uid: zp.uid});
            this.checkTrust(tb, zp);
        } else {
            this.startLaoPai(tb);
        }
    }
}

//码宝（压上一把赢分）
GameCodeLaoYanCai.prototype.pkMaBao = function (pl, msg, session, next, tb) {
    if (pl.lastWinOne > 0) {
        var msg = {};
        msg.jiazhuNum = parseInt(pl.lastWinOne);
        this._mjJiazhu(pl, msg, session, next, tb);
        msg.uid = pl.uid;
        tb.NotifyAll('pkMaBao', msg);
        cloneDataAndPush(tb.mjlog, 'pkMaBao', msg);
        pl.mabao = 1;  //玩家点击了码宝
    } else {
        var msg = {};
        msg.jiazhuNum = parseInt(tb.createParams.diZhu);
        this._mjJiazhu(pl, msg, session, next, tb);
    }
}

//炸牌
GameCodeLaoYanCai.prototype.pkZhaPai = function (pl, msg, session, next, tb) {
    var tData = tb.tData;
    if(tData.tState == TableState.waitZhaPai &&  pl.mjState == TableState.waitZhaPai) {
        if (msg.zhapai) {
            tb.NotifyAll('pkZhaPai', {uid:pl.uid});
            cloneDataAndPush(tb.mjlog, 'pkZhaPai', {uid:pl.uid});
            if (pl.uid == tData.uids[tData.zhuang]) {
                this._startTanPai(tb);
            } else {
                //闲家炸牌既与庄家比牌
                this._pkBiPai(tb, pl.uid);
                var msg = {uid:pl.uid, mjhand:pl.mjhand, handScore:pl.handScore, hasBiPai:pl.hasBiPai, biPaiResult:pl.biPaiResult};
                tb.NotifyAll('nfTanPai', msg);
                cloneDataAndPush(tb.mjlog, 'nfTanPai', msg);
                //延时再操作
                this._continueLaoPai(tb);
            }
        } else {
            pl.notify("buZhaPai", {});
            if (pl.uid == tData.uids[tData.zhuang]) {
                this.startLaoPai(tb);
            } else {
                this.rotateLaoPai(tb);
            }
        }

        this.clearTrustTimer(tb, pl);
    }
}

//开始捞牌，从庄家下一家开始逐个提示捞牌(或炸开)
GameCodeLaoYanCai.prototype.startLaoPai = function(tb) {
    var tData = tb.tData;
    tData.curPlayer = tData.zhuang;
    tData.tState = TableState.waitLaoPai;
    this.autoLaoPai = 0;
    this.rotateLaoPai(tb);
}

//轮流捞牌
GameCodeLaoYanCai.prototype.rotateLaoPai = function(tb) {
    var tData = tb.tData;
    tData.tState = TableState.waitLaoPai;
    do {
        tData.curPlayer = (tData.curPlayer + 1) % tData.uids.length;
        var curUid = tData.uids[tData.curPlayer];
        if (tData.rungingUids.indexOf(curUid) < 0) {
            continue;
        }

        var sendData = {};
        var hasBiPaiCount = 0;  //已经比牌的玩家人数
        for (var i = 0; i < tData.rungingUids.length; i++)
        {
            var pl = tb.players[tData.rungingUids[i]];
            sendData[pl.uid] = pl.hasBiPai;
            if (pl.hasBiPai) hasBiPaiCount++;
        }
        var curPly = tb.getPlayer(tData.uids[tData.curPlayer]);
        var curScore = this.majiang.calHandScore(curPly.mjhand);
        if (curScore >= 4 && curScore <= 7) {
            //提示手动捞牌
            curPly.mjState = TableState.waitLaoPai;
            
            if ((tData.curPlayer != tData.zhuang) || (hasBiPaiCount < tData.rungingUids.length-1)) {
                curPly.notify("nfLaoPai", sendData);
                this.checkTrust(tb, curPly);
            } else {
                this._startTanPai(tb);
            }
            break;
        } else {
            if (curScore <= 3) {
                if ((tData.curPlayer != tData.zhuang) || (hasBiPaiCount < tData.rungingUids.length-1)) {
                    //自动捞牌
                    var newCard = tb.cards[tData.cardNext++];
                    curPly.mjhand.push(newCard);
                    var msg = {uid:curPly.uid, newCard:newCard};
                    tb.NotifyAll("pkLaoPai", msg);
                    cloneDataAndPush(tb.mjlog, 'pkLaoPai', msg);
                    this.autoLaoPai++;
                    curPly.mjState = TableState.waitTanPai;
                }
            } 
            else if (curScore >= 8){
                //提示闲家炸牌
                if (tData.curPlayer != tData.zhuang) {
                    tData.tState = TableState.waitZhaPai;
                    curPly.mjState = TableState.waitZhaPai;
                    curPly.notify("nfZhaPai", {uid: curPly.uid});
                    this.checkTrust(tb, curPly);
                    break;
                }
            }
            
            //所有人自动捞牌，或所有玩家操作完毕
            var maxPlayer = tData.rungingUids.length;
            if (this.autoLaoPai >= maxPlayer || tData.curPlayer == tData.zhuang) {
                this._startTanPai(tb);
                this.autoLaoPai = 0;
                break;
            }

            //这里要延时一下
            this._continueLaoPai(tb);
            break;
        }
    } while (tData.curPlayer != tData.zhuang);
}

//继续捞牌
GameCodeLaoYanCai.prototype._continueLaoPai = function (tb) {
    //延时2.5秒再操作
    var _this = this;
    var timeId = null;
    timeId = setTimeout(() => {
        clearTimeout(timeId);
        _this.rotateLaoPai(tb);
    }, 2500);
}

//捞牌
GameCodeLaoYanCai.prototype.pkLaoPai = function (pl, msg, session, next, tb) {
    var tData = tb.tData;
    if(tData.tState == TableState.waitLaoPai && pl.mjState == TableState.waitLaoPai) {
        if (msg.laopai) {
            var newCard = tb.cards[tData.cardNext++];
            pl.mjhand.push(newCard);
            var msg = {uid:pl.uid, newCard:newCard};
            tb.NotifyAll("pkLaoPai", msg);
            cloneDataAndPush(tb.mjlog, 'pkLaoPai', msg);
        } else {
            if (tData.curPlayer != tData.zhuang) {
                tb.NotifyAll("buLaoPai", {uid:pl.uid});
            }
        }
        pl.mjState = TableState.waitTanPai;

        if (tData.curPlayer != tData.zhuang) {
            //延时再操作
            this._continueLaoPai(tb);
        } else {
            this._startTanPai(tb);
        }

        this.clearTrustTimer(tb, pl);
    }
}

// 用户比牌
GameCodeLaoYanCai.prototype.pkCompareCard = function(pl, msg, session, next, tb) {
    var pkPly = tb.getPlayer(msg.uid);
    if (pkPly) {
        this._pkBiPai(tb, msg.uid);
        var sendData = {
            zhuangUid: pl.uid,
            uid: msg.uid,
            mjhand: pkPly.mjhand, 
            handScore: pkPly.handScore, 
            hasBiPai: pkPly.hasBiPai, 
            biPaiResult: pkPly.biPaiResult
        };
        //发送数据
        tb.NotifyAll("pkCompareCard", sendData);
        cloneDataAndPush(tb.mjlog, "pkCompareCard", sendData);
    }
    this.clearTrustTimer(tb, pl);
    this.checkTrust(tb, pl);
}

//比牌
GameCodeLaoYanCai.prototype._pkBiPai = function (tb, uid)
{
    var tData = tb.tData;
    //记录当前局中最大的抢庄倍数
    var maxCallBankerTimes = 1;
    var maxPlayer = tData.rungingUids.length;
    for (let i = 0; i < maxPlayer; i++) 
    {
        let p = tb.getPlayer(tData.rungingUids[i]);
        if(p.callBankNum > maxCallBankerTimes)
        {
            maxCallBankerTimes = p.callBankNum;
        }
    }

    var zp = tb.getPlayer(tData.uids[tData.zhuang]);
    zp.handScore = this.majiang.calAllScore(zp.mjhand);
    var zhuangScore = zp.handScore;
    var zPoint = this.majiang.handPoint(zp.handScore);

    var pl = tb.getPlayer(uid);
    if (pl && pl != zp) {
        pl.handScore = this.majiang.calAllScore(pl.mjhand);
        var pPoint = this.majiang.handPoint(pl.handScore);
                    
        if(zPoint != pPoint) {
            if (pPoint < zPoint)
            {
                var lose = this.majiang.calRate(zhuangScore) * pl.jiazhuNum * maxCallBankerTimes;
                if(pl.info.happyBean != null)
                {
                    var leftHappyBean = pl.info.happyBean + pl.winall;
                    if(leftHappyBean < lose) lose = leftHappyBean;
                }

                //不能以小博大，超过自带分
                if (lose > (zp.info.happyBean + zp.winall)) {
                    lose = zp.info.happyBean + zp.winall;
                }
                
                pl.winone -= lose;
                zp.winone += lose;
            
                pl.roomStatistics[3]++;
                zp.roomStatistics[4]++;

                pl.biPaiResult = -1;
            }
            else
            {
                var win = this.majiang.calRate(pl.handScore) * pl.jiazhuNum * maxCallBankerTimes;
                if(zp.info.happyBean)
                {
                    var leftHappyBean = zp.info.happyBean + zp.winall;
                    if(leftHappyBean < win) win = leftHappyBean;
                }

                //不能以小博大，超过自带分
                if (win > (pl.info.happyBean + pl.winall)) {
                    win = pl.info.happyBean + pl.winall;
                }
                
                pl.winone += win;
                zp.winone -= win;

                zp.roomStatistics[3]++;
                pl.roomStatistics[4]++; 

                pl.biPaiResult = 1;
            }
        }

        pl.hasBiPai = true;
    }
}

//所有玩家摊牌
GameCodeLaoYanCai.prototype._startTanPai = function (tb) {
    var tData = tb.tData;
    tData.tState = TableState.waitTanPai;
    var maxPlayer = tData.rungingUids.length;
    for (var i = 0; i < maxPlayer; i++)
    {
        var pl = tb.players[tData.rungingUids[i]];
        this._pkTanPai(pl, tb);
    }
}

//亮牌
GameCodeLaoYanCai.prototype._pkTanPai = function (pl, tb)
{
    var tData = tb.tData;
    if(tData.tState == TableState.waitTanPai)
    {
        pl.mjState = TableState.roundFinish;
        pl.handScore = this.majiang.calAllScore(pl.mjhand);
        //数据统计
        if (!pl.hasBiPai) {
            var msg = {uid:pl.uid, mjhand:pl.mjhand, handScore:pl.handScore};
            tb.NotifyAll('pkTanPai', msg);
            cloneDataAndPush(tb.mjlog, 'pkTanPai', msg);
        }

        var bAllFinish = true;
        for (var i = 0; i < tData.rungingUids.length; i++)
        {
            var p = tb.players[tData.rungingUids[i]];
            if(p.mjState != TableState.roundFinish)
            {
                bAllFinish = false;
                break;
            }
        }
        if(bAllFinish)
        {
            var _this = this;
            var timeId = null;
            timeId = setTimeout(() => {
                clearTimeout(timeId);
                _this.EndGame(tb);
            }, 2000);
        }
    }
};

//每局播完金币动画之后需手动准备
GameCodeLaoYanCai.prototype.mjPassCard = function (pl, msg, session, next, tb)
{
    var tData = tb.tData;
    if (tData.tState == TableState.waitReady && pl.mjState == TableState.waitReady)
    {
        this.clearTrustTimer(tb, pl);
        pl.mjState = TableState.isReady;
        tb.NotifyAll('onlinePlayer', {uid: pl.uid, onLine: true, mjState: pl.mjState});
        if(tData.rungingUids.indexOf(pl.uid) < 0 && !tData.gameRuning)
        {
            tb.tData.rungingUids.push(pl.uid);
        }

        if (tb.AllPlayerCheck(function (p) {
                return p.mjState == TableState.isReady;
            }))
        {
            tb.tData.freeBeginStart = true;
            tb.tData.hasReadyBtn = true;
            tb.startGame();
        }
    }
};

GameCodeLaoYanCai.prototype.EndRoom = function (tb, msg) {
    //logger.debug("========解散房间========" );
    tb.calculateTableFinalScore();
    var playInfo = null;
    if (tb.tData.roundNum > -2) {
        if (tb.tData.roundNum != tb.createParams.round) 
        {
            var tData = tb.tData;
            playInfo = {
                gametype: tData.gameType,
                owner: tData.owner,
                money: tb.createParams.money,
                now: utils.dateFormat(new Date(), 'yyyy-MM-dd hh:mm:ss'),
                tableid: tb.tableid,
                players: []
            };
            tb.AllPlayerRun(function(p) {
                var pinfo = {};
                pinfo.uid = p.uid;
                pinfo.winall = p.winall;
                pinfo.nickname = p.info.nickname;
                pinfo.money = p.info.money;
                playInfo.players.push(pinfo);
                p.info.lastGameTime = new Date().getTime();
                modules.user.updateInfo({userId: p.uid, lastGameTime: p.info.lastGameTime});
            });
        }
        if (msg)
        {
            if (playInfo) msg.playInfo = playInfo;
            if(!msg.showEnd) msg.showEnd = tb.tData.roundNum != tb.createParams.round;
            msg.players = tb.collectPlayer('lastOffLineTime');
            msg.serverTime = Date.now();
            tb.NotifyAll("endRoom", msg);
        }
        tb.tData.gameRuning = false;
        tb.AllPlayerRun((p) => {
            this.clearTrustTimer(tb, p);
        });

        tb.validTableEndDo();
        tb.SetTimer();
        logger.debug("++++++++++++++++++++++++++++==== bobo tb.tData.roundNum = -2 ");
        tb.tData.roundNum = -2;
        this.DestroyTable(tb);
        tb.endVipTable(tb.tData);
    }
    return playInfo;
};

GameCodeLaoYanCai.prototype.EndGame = function (tb, pl, byEndRoom)
{
    var tData = tb.tData;
    tData.tState = TableState.roundFinish;
    //logger.debug("========GameCodeLaoYanCai.prototype.endGame========" + JSON.stringify(tData.uids)); 
    // var nDiFen = tb.createParams.difen || 1;

    //算分
    var that = this;
    if (byEndRoom)
    {
        tb.showDissmissDesc();
    }
    else
    {
        var zp = tb.getPlayer(tData.uids[tData.zhuang]);
        var zhuangScore = zp.handScore;
        var zPoint = that.majiang.handPoint(zp.handScore);
        var maxPlayer = tData.rungingUids.length;

        //记录当前局中最大的抢庄倍数
        var maxCallBankerTimes = 1;
        for (var i = 0; i < maxPlayer; i++) 
        {
            var p = tb.getPlayer(tData.rungingUids[i]);
            if(p.callBankNum > maxCallBankerTimes)
            {
                maxCallBankerTimes = p.callBankNum;
            }
        }

        if(tb.createParams.clubId)
        {
            var totalWin = 0;
            var totlLose = 0;
            var userWin = {};
            var allUserWinInfo = [];

            for (var i = 0; i < maxPlayer; i++) 
            {
                var p = tb.getPlayer(tData.rungingUids[i]);
                if (p != zp)
                {
                    if (p.hasBiPai) {
                        if (p.winone > 0) {
                            totlLose += p.winone;
                        } else if (p.winone < 0) {
                            totalWin += Math.abs(p.winone);
                        }
                        continue;
                    }

                    var score = p.handScore;
                    var pPoint = that.majiang.handPoint(p.handScore);
                    
                    if(zPoint == pPoint)
                    {
                        if(tData.areaSelectMode["tongPaiXingDaXiao"] == 1)             //打合
                        {
                            continue;
                        }
                    }
                    else
                    {
                        if (pPoint < zPoint)
                        {
                            var lose = that.majiang.calRate(zhuangScore) * p.jiazhuNum * maxCallBankerTimes;
                            if(p.info.happyBean)
                            {
                                var leftHappyBean = p.info.happyBean + p.winall;
                                if(leftHappyBean < lose) lose = leftHappyBean;
                            }

                            //不能以小博大，超过自带分
                            if (lose > (zp.info.happyBean + zp.winall)) {
                                lose = zp.info.happyBean + zp.winall;
                            }
                            
                            p.winone -= lose;
                            zp.winone += lose;
                        
                            p.roomStatistics[3]++;
                            zp.roomStatistics[4]++;
                            totalWin += lose;
                        }
                        else
                        {
                            var win = that.majiang.calRate(score) * p.jiazhuNum * maxCallBankerTimes;

                            //不能以小博大，超过自带分
                            if (win > (p.info.happyBean + p.winall)) {
                                win = p.info.happyBean + p.winall;
                            }
                            
                            totlLose += win;
                            userWin[p.uid] = win;
                        
                            var winInfp = {};
                            winInfp.uid = p.uid;
                            winInfp.win = win;
                            allUserWinInfo.push(winInfp);
                        }
                    }
                }
            }
            
            //庄家够输
            if(totlLose > 0 &&  (totalWin + zp.winall + zp.info.happyBean > totlLose))
            {
                for (var i = 0; i < maxPlayer; i++) 
                {
                    var p = tb.getPlayer(tData.rungingUids[i]);
                    if (p != zp && !p.hasBiPai) 
                    { 
                        var pPoint = that.majiang.handPoint(p.handScore);
                        if (pPoint > zPoint)
                        {
                            p.winone += userWin[p.uid];
                            zp.winone -= userWin[p.uid];
                            
                            zp.roomStatistics[3]++; //输赢统计
                            p.roomStatistics[4]++;
                        }
                    }
                }
            }
            else
            {
                allUserWinInfo.sort(function(a,b){
                    return b.win - a.win;
                })
            
                for(var i = 0; i < allUserWinInfo.length;i++)
                {
                    var uid = allUserWinInfo[i].uid;
                    var win = allUserWinInfo[i].win;
                    var p = tb.getPlayer(uid);
                    //够输
                    if(zp.info.happyBean + zp.winone + zp.winall > win)
                    {
                        p.winone += win;
                        zp.winone -= win;
                        
                        zp.roomStatistics[3]++; //输赢统计
                        p.roomStatistics[4]++; 
                    }
                    else
                    {
                        win = zp.info.happyBean + zp.winone + zp.winall;
                        p.winone += win;
                        zp.winone -= win;
                        
                        zp.roomStatistics[3]++; //输赢统计
                        p.roomStatistics[4]++;
                        break;
                    }
                }
            }
        }
        else
        {
            for (var i = 0; i < maxPlayer; i++) 
            {
                var p = tb.getPlayer(tData.rungingUids[i]);
                if (p != zp && !p.hasBiPai)
                {
                    var score = p.handScore;
                    var pPoint = that.majiang.handPoint(p.handScore);

                    if(zPoint == pPoint)
                    {
                        if(tData.areaSelectMode["tongPaiXingDaXiao"] == 1)                 //算平局
                        {
                            continue;
                        }
                        else if(tData.areaSelectMode["tongPaiXingDaXiao"] == 2)                 //庄家赢
                        {
                            var lose = that.majiang.calRate(zhuangScore) * p.jiazhuNum * maxCallBankerTimes;
                            p.winone -= lose;
                            zp.winone += lose;
                            p.roomStatistics[3]++;
                            zp.roomStatistics[4]++;
                        }
                    }
                    else
                    {
                        if (pPoint > zPoint)
                        {
                            var win = that.majiang.calRate(score) * p.jiazhuNum * maxCallBankerTimes;
                            p.winone += win;
                            zp.winone -= win;
                            zp.roomStatistics[3]++; //输赢统计
                            p.roomStatistics[4]++; 
                        }
                        else
                        {
                            var lose = that.majiang.calRate(zhuangScore) * p.jiazhuNum * maxCallBankerTimes;
                            p.winone -= lose;
                            zp.winone += lose;
                            p.roomStatistics[3]++;
                            zp.roomStatistics[4]++;
                        }
                    }
                }
            }
        }
    }

    var maxPlayer  = tData.rungingUids.length;
    for (var i = 0; i < maxPlayer; i++)
    {
        var p = tb.getPlayer(tData.rungingUids[i]);
        p.mjState = TableState.roundFinish;
        p.winall += p.winone;
        //保存上一局输赢分（如果打平下一局继续码宝）
        if (p.winone > 0) {
            p.lastWinOne = p.winone
        } else if (p.winone < 0) {
            p.lastWinOne = 0;
        }
    }

    for (var i = 0; i < maxPlayer; i++) 
    {
        var p = tb.getPlayer(tData.rungingUids[i]);
        this.clearTrustTimer(tb, p);
    }
    if (tData.roundAll == tData.roundNum) {
        tb.firstRoundEndDo();
    }
    tb.perRoundEndDo();
    tData.roundNum--;
    var roundEnd = {
        players: tb.collectPlayer('mjdesc', 'mjhand', 'handScore', 'selectedCards', 'winone', 'winall', 'info', 'jiazhuNum', 'roomStatistics' ,'lastOffLineTime'),
        tData: tData,
        roundEndTime: utils.dateFormat(new Date(), 'yyyy-MM-dd hh:mm:ss'),
        isDismiss: !!byEndRoom,
        rungingUids : JSON.parse(JSON.stringify(tData.rungingUids))
    };

    cloneDataAndPush(tb.mjlog, "roundEnd", roundEnd);//一局结束
    var playInfo = null;
    if (tData.roundNum == 0)
        playInfo = this.EndRoom(tb);//结束
    if (playInfo) roundEnd.playInfo = playInfo;

    var tanPaiCount = 0;
    //清理下注，抢庄，拼牛的状态
    for (var i = 0; i < maxPlayer; i++) 
    {
        var p = tb.getPlayer(tData.rungingUids[i]);
        p.callBankNum = null;
        p.jiazhuNum = null;
        if (!p.hasBiPai)
            tanPaiCount++;
    }
    roundEnd.tanPaiCount = tanPaiCount;  //统计摊牌人数，客户端做延时飞金币
    if (tb.nntimerId) {
        clearTimeout(tb.nntimerId);
        tb.nntimerId = null;
    }
    //一局打完，所有的人中途加入状态置空
    tb.AllPlayerRun( p => {
        p.centerJoinNN = false;
    });
    tb.NotifyAll("roundEnd", roundEnd);
    ////logger.debug("===========roundEnd==" + JSON.stringify(roundEnd));

    tb.AllPlayerRun(function(pl)
    {
        if(tData.rungingUids.indexOf(pl.uid) < 0)
        {
            tData.rungingUids.push(pl.uid); 
        }
        if(tb.createParams.clubId)
        {
            //玩家小于离场分移除有效玩家列表
            var leftHappyBean = pl.info.happyBean + pl.winall;
            if(pl.info.happyBean != null && leftHappyBean <= tb.createParams.antiAddictionDissolveLimitScore)
            {
                tData.rungingUids.splice(tData.rungingUids.indexOf(pl.uid), 1);
            }

            //离线玩家移出列表
            if (!pl.onLine) {
                tData.rungingUids.splice(tData.rungingUids.indexOf(pl.uid), 1);
            }
        }

        //检查托管
        // if(pl.trust){
        //     pl.trustWholeTimes -= 1;
        // }
        // if(pl.trustWholeTimes <= 0)
        // {
        //     tData.roundNum = 1;
        //     var des = "玩家:"+ pl.uid + "连续托管结束，房间自动解散";
        //     that.EndRoom(tb, {reason:4, des: des});
        //     return;
        // }
    });

    //有效玩家列表的个数小于等于1时直接解散房间
    if(tData.rungingUids.length <= 1)
    {
        tData.roundNum = 1;
        var des = "有效玩家少于2人，房间自动解散";
        this.EndRoom(tb, {reason:4, des: des});
        return;
    }

    //2局之间自动开始游戏
    var delayTime = tanPaiCount > 0 ? tanPaiCount * 2000 : 3000;
    if(tData.roundNum > 0)
    {
        var timeId = null;
        timeId = setTimeout(() => {
            clearTimeout(timeId);

            tb.NotifyAll('clearClientUI', {});
            cloneDataAndPush(tb.mjlog, 'clearClientUI', {});

            // 每小局结束等待玩家手动准备
            tData.tState = TableState.waitReady;
            tb.AllPlayerRun(function (p) {
                p.mjState = TableState.waitReady;
                that.checkTrust(tb, p);
            });
            tb.NotifyAll("waitReady", {});

            // tb.AllPlayerRun(function(pl) {
            //     pl.mjState = TableState.isReady;
            // });

            // if (tb.AllPlayerCheck(function (pl){
            //         return pl.mjState == TableState.isReady;
            //     }))
            // {
            //     //下一局开始时，房间中的人中途加入的状态需要清理
            //     tb.AllPlayerRun(function(pl) {
            //         pl.centerJoinNN = false;
            //     }); 
            //     tb.startGame();
            // }
        }, delayTime);
    }
};

GameCodeLaoYanCai.prototype.doTrustAction = function(tb, pl) {
    if (pl.mjState == TableState.waitZhaPai) {
        var msg = {};
        msg.zhapai = false;
        this.pkZhaPai(pl, msg, null, null, tb);
    } else if (pl.mjState == TableState.waitLaoPai) {
        var msg = {};
        msg.laopai = false;
        this.pkLaoPai(pl, msg, null, null, tb);
    } else if (pl.mjState == TableState.waitReady) {
        this.mjPassCard(pl, {}, null, null, tb);
    }
};

GameCodeLaoYanCai.prototype.doTrustCountdown = function(tb, pl) {
    if (pl.trustTimer_bp2) { // 容错处理
        clearTimeout(pl.trustTimer_bp2);
    }
    pl.trustTimer_bp2 = setTimeout(function () {
        pl.trustTimer_bp2 = null;
        this.doTrustAction(tb, pl);
    }.bind(this), 1100);
};

GameCodeLaoYanCai.prototype.beTrustCountdown = function(tb, pl) {
    var tData = tb.tData;
    if (tData.trustEnd == -1) {
        tData.trustEnd = Date.now() + (tData.areaSelectMode.trustTime + 1) * 1000;
        tb.NotifyAll("trustTime", {trustEnd: tData.trustEnd});
    }
    //logger.debug("倒计时 玩家%d的托管时间%d", pl.uid, tData.trustEnd);
    pl.trustEnd = tData.trustEnd;
    if (pl.trustTimer_bp) {
        clearTimeout(pl.trustTimer_bp);
    }
    pl.trustTimer_bp = setTimeout(function () {
        pl.trustTimer_bp = null;
        pl.trust = true;

        tb.NotifyAll("beTrust", {uid: pl.uid});
        cloneDataAndPush(tb.mjlog, 'beTrust', {uid: pl.uid});
        tData.trustEnd = -1;
        pl.trustEnd = -1;
        this.doTrustCountdown(tb, pl);
    }.bind(this), tData.areaSelectMode.trustTime * 1000);
};

GameCodeLaoYanCai.prototype.checkTrust = function(tb, pl) {
    if (!(tb.tData.areaSelectMode.trustTime > 0)) {
        return;
    }

    if (pl.mjState == TableState.waitCard || pl.mjState == TableState.roundFinish) {
        return;
    }
    var countdown = tb.tData.areaSelectMode.trustTime;
    tb.NotifyAll("trustTip", {tipCountDown: countdown, uid: pl.uid});
    //logger.debug("检测托管 玩家%d是否托管%d", pl.uid, pl.trust);
    pl.trust ? this.doTrustCountdown(tb, pl) : this.beTrustCountdown(tb, pl);
};

GameCodeLaoYanCai.prototype.clearTrustTimer = function(tb, pl)
{
    var tData = tb.tData;
    if (pl.trustTimer_bp)
    { 
        clearTimeout(pl.trustTimer_bp);
        pl.trustTimer_bp = null;
        //pl.trust = false;
        pl.trustEnd = -1;

        var bAllTrustEnd = true;
        var maxPlayer  = tData.rungingUids.length;
        for (var i = 0; i < maxPlayer; i++) 
        {
            var p = tb.getPlayer(tData.rungingUids[i]);
            if(p.trustEnd != -1)
            {
                bAllTrustEnd = false;
                break;
            }
        }
        if(bAllTrustEnd)
        {
            tb.tData.trustEnd = -1;
            tb.NotifyAll("trustTime", {trustEnd: tb.tData.trustEnd});
        }
    }

    if (pl && pl.trustTimer_bp2) {
        clearTimeout(pl.trustTimer_bp2);
        pl.trustTimer_bp2 = null;
    }
};

// 取消托管接口
GameCodeLaoYanCai.prototype.cancelTrust = function(pl, msg, session, next, tb)
{
    if (!pl.trust)
    {
        return;
    }
    pl.trust = false;
    this.clearTrustTimer(tb,pl);
    // pl.trustWholeTimes = [1,2,100][tb.tData.areaSelectMode.trustWay || 0];
    tb.NotifyAll('cancelTrust', {uid: pl.uid});
    cloneDataAndPush(tb.mjlog, 'cancelTrust', {uid: pl.uid});
};

GameCodeLaoYanCai.prototype.DestroyTable = function (tb)
{
    if (tb.PlayerCount() == 0 && tb.tData.roundNum == -2)
    {
        tb.tData.roundNum = -3;
        tb.Destroy();
    }
};

GameCodeLaoYanCai.prototype.countZhuang = function (tData, tb)
{
    //检测是否有自动下注 
    var tData = tb.tData;
    switch (tData.areaSelectMode["zhuangType"])
    {
        case 1: //房主坐庄
            tData.zhuang = 0;
            break;
        case 2 : //顺序轮庄
            if(tb.createParams.clubId)
            {
                var randIdx  = (tData.zhuang + 1) % tData.rungingUids.length;
                var tmpZpUid  = tData.rungingUids[randIdx];
                for(var i = 0; i < tData.uids.length;i++)
                {
                    if(tData.uids[i] == tmpZpUid)
                    {
                        tData.zhuang = i;
                        break;
                    }
                }
            }
            else
            {
                tData.zhuang = (tData.zhuang + 1) % tData.rungingUids.length;
            }
            break;
    }
};

GameCodeLaoYanCai.prototype.NNRestCard = function (pl, msg, session, next, tb) {
    var tData = tb.tData;
    if(tData.uids.indexOf(pl.uid) == -1){
        return;
    }

    if(pl.isAuth)
    {
        logger.debug("++++ NNRestCard        进来了3");
        pl.getCard = [];
        for(var i = 0; i < 5; i++)
        {
            if(msg.cardlist[i])
            {
                pl.getCard.push(msg.cardlist[i]);
            }
        }
    }
};

GameCodeLaoYanCai.prototype.collectPlayer = function(tb)
{
    return tb.collectPlayer(
        'trust',
        'info',
        'mjState',
        'onLine',
        'lastOffLineTime',
        'delRoom',
        'winall',
        'roomStatistics',
        'jiazhuNum',                    // 加注
        'locationMsg',                  // 位置信息
        'locationApps',                 // 定位修改App
        'winone',
        'lastWinOne',                   // 上一局赢分
        'mjhand',
        'hasBiPai',                     // 是否与庄家比牌（闲家炸牌也算）
        'biPaiResult',                  // 比牌结果
        'centerJoinNN'
    );
}

module.exports = GameCodeLaoYanCai;