//个旧麻将
//花牌
var flowerCards = [];

function GameCodeGeJiuMJ(majiang, app) {
    this.majiang = majiang;
    this.app = app;
};

GameCodeGeJiuMJ.prototype.initAreaSelectMode = function (tb) {
    var tData = tb.tData;
    tData.gameCnName = "个旧麻将";
    tData.maxPlayer = tb.createParams.maxPlayer;        // 人数  4人   3人    2人   自由人
    tData.areaSelectMode.hunType = 0;//tb.createParams.hunType;          // 混牌类型    0：无混    1：单混    2：双混      3：红中混
    tData.areaSelectMode.windCard = tb.createParams.windCard;          // 风牌    true带风   false不带
    tData.areaSelectMode.canRob = tb.createParams.canRob;         //抢杠胡
    tData.areaSelectMode.qiangGangQuanBao = tb.createParams.qiangGangQuanBao;         //抢杠全包
    tData.areaSelectMode.gangBaoQuanBao = tb.createParams.gangBaoQuanBao;         //杠爆全包
    tData.areaSelectMode.onlyZiMoHu = tb.createParams.onlyZiMoHu;         //仅自摸胡
    tData.areaSelectMode.isQiDui = tb.createParams.isQiDui;         //胡七对
    tData.areaSelectMode.qiDuiJiaBei = tb.createParams.qiDuiJiaBei;         //七对加倍
    tData.areaSelectMode.genZhuang = tb.createParams.genZhuang;         //跟庄
    tData.areaSelectMode.daHu = tb.createParams.daHu;         //大胡

    tData.areaSelectMode.maCount = tb.createParams.maCount; //买马 ，1表示1码全中

    tData.areaSelectMode.fanpigu = tb.createParams.fanpigu;         //翻屁股
    tData.areaSelectMode.shoudailong = tb.createParams.shoudailong;         //手逮一条龙
    tData.areaSelectMode.luodilong = tb.createParams.luodilong;         //落地龙
    tData.areaSelectMode.doudizhu = tb.createParams.doudizhu;         //斗地主
    tData.areaSelectMode.doudizhufanbei = tb.createParams.doudizhufanbei;         //地主翻倍
    fanpiguCards = [];
    checkfanpiguCard = 0;
    mofanpigupai = false
    tData.areaSelectMode.difen = tb.createParams.difen;     // 结算底分
    tData.areaSelectMode["duoHu"] = true;                                //一炮多响
    tData.areaSelectMode["fengDing2"] = "fengDing2" in tb.createParams ? tb.createParams.fengDing2 : 0;// 封顶
    tData.douDiZhuCard = 0; // 庄家出的第一张牌
    tData.douDiZhuState = false; // 斗地主状态
};

GameCodeGeJiuMJ.prototype.initPlayer = function (pl) {
    pl.mopai = [];                          // 胡牌后摸的牌
    pl.isGang = 0;                          // 杠(用来记录是否杠后炮)
    pl.gangScore = 0;                       // 杠分
    pl.perScore = 0;                        // 输家的输分
    pl.trust = false;
    pl.tPutCard = false;
    pl.skipPeng = [];
    pl.skipHu = [];
    pl.maScore = 0;     //码分
    pl.isGenZhuang = false;//是否跟庄
    pl.isFirstPut = true;   //首次出牌
    pl.winone = 0;
    pl.genZhuangScore = 0;//跟庄分
    pl.consecutiveZiPutCount = 0; // 连续出字牌计数
    pl.lastPutCard = null; // 记录上一次出牌
    pl.tangZiPutCards = []; // 塘子牌可胡出牌记录
    pl.consecutiveZiPutCards = []; //记录连续打出的字牌
    pl.isTangZiQiDui = false; // 塘子小七对状态
    pl.tangZiType = 0; // 塘子牌型状态 (1:烂牌, 2:一般高, 3:七星, 4:七星一般高)
    pl.gangShangCount = 0; // 添加杠上开花计数
    pl.GangKaiHua = 0; // 杠上开花连续记录
    pl.hasOperated = false; // 是否进行了吃碰杠操作
    pl.douDiZhuFailed = false; // 是否斗地主失败
    pl.douDiZhuScore = 0; // 添加斗地主得分
    pl.fanpiguCount = 0; // 添加翻屁股牌计数
};


GameCodeGeJiuMJ.prototype.doBeforeSendHands = function (tb) {
    //混子牌
    var tData = tb.tData;
    tData.firstPutCardOfZhuang = 0;
    tData.sendCardCountOfZhuang = 0;//庄家发牌数量
    console.log("个旧麻将初始化牌堆...", tData.areaSelectMode.fanpigu);

    // 设置翻屁股牌（最后两张）
    if (tData.areaSelectMode.fanpigu) {
        tData.fanpiguCards = [
            tb.cards[tb.cards.length - 2],
            tb.cards[tb.cards.length - 1]
        ];
        // 从牌堆中移除翻屁股牌
        tb.cards.length = tb.cards.length - 2;
        console.log("翻屁股牌", tData.fanpiguCards);
    }


    if (tData.areaSelectMode.hunType == 0) {//无混子
        return;
    }

    if (tData.areaSelectMode.hunType == 3) {//红中混
        tData.hunCard = 71;
        return;
    }

    var laiZiPi = tb.cards[tData.cardNext];
    var color = Math.floor(laiZiPi / 10);
    var value = laiZiPi % 10;
    if (tData.areaSelectMode.hunType >= 1) {
        if (color > 3) {
            color = color == 9 ? 3 : (color + 1);
        } else {
            value = value == 9 ? 1 : (value + 1);
        }
        tData.hunCard = 10 * color + value;
    }

    if (tData.areaSelectMode.hunType == 2) {
        if (color > 3) {
            color = color == 9 ? 3 : (color + 1);
        } else {
            value = value == 9 ? 1 : (value + 1);
        }
        tData.hunCard1 = 10 * color + value;
    };
    //初始牌堆
    cloneDataAndPush(tb.mjlog, "logCardHeap", {
        action: "init",
        cards: tb.cards.slice()
    });

};

GameCodeGeJiuMJ.prototype.isHardFlower = function (card) {
    return flowerCards.indexOf(card) >= 0;
};

// 获取总的牌的数量
GameCodeGeJiuMJ.prototype.getCardTotalNum = function (tb) {
    var cardCount = tb.cards.length;
    var tData = tb.tData;
    return cardCount - tData.areaSelectMode.maCount;
};

// 获取胡的类型, 返回true还是false
GameCodeGeJiuMJ.prototype.getHuType = function (tb, pl, cd, hun) {

    logger.debug("=====------算胡开始------=====");
    var canHu = this.majiang.canHu(tb, pl, pl.mjhand, cd, hun);
    return canHu;
};

// 获取状态
GameCodeGeJiuMJ.prototype.getEatFlag = function (tb, pl) {
    logger.debug("getEatFlag===========");
    var tData = tb.tData;
    var cd = tData.lastPutCard;
    var leftCard = this.getCardTotalNum(tb) - tData.cardNext;
    var eatFlag = 0;

    if (this.getHuType(tb, pl, cd, tData.hunCard) && (tData.curPlayer == tData.uids.indexOf(pl.uid) || pl.skipHu.indexOf(cd) == -1)) {
        eatFlag += 8;
    }

    if (leftCard > 0) {
        // 检查是否可以明杠：手中有三张与最后打出的牌相同的牌
        var count = 0;
        for (var i = 0; i < pl.mjhand.length; i++) {
            if (pl.mjhand[i] === cd) {
                count++;
            }
        }
        if (count >= 3) {
            eatFlag += 4; // 允许明杠
        }
    }

    if (!pl.isTing && leftCard > 0 && this.majiang.canPeng(pl.mjhand, cd) && pl.skipPeng.indexOf(cd) == -1)//碰
    {
        eatFlag += 2;
    }
    if (leftCard > 0 && (tData.uids[(tData.curPlayer + 1) % tData.maxPlayer] == pl.uid) && this.majiang.canChi(pl.mjhand, cd).length > 0) { // 吃
        eatFlag += 1;
    }

    //不支持吃牌

    return eatFlag;
};



// 发牌
GameCodeGeJiuMJ.prototype.sendNewCard = function (tb) {
    logger.debug("sendNewCard=====sendNewCard=====");
    var tData = tb.tData;
    console.log("");

    var cards = tb.cards;
    var g_this = this;

    if (tb.AllPlayerCheck(function (pl) {
        logger.debug("sendNewCard=====pl.uid:" + pl.uid);
        logger.debug("sendNewCard=====pl.mjState==TableState.waitCard:" + (pl.mjState == TableState.waitCard));
        return pl.mjState == TableState.waitCard;
    }
    )
    ) {
        // 荒庄留的牌的数量
        logger.debug("sendNewCard=====TableState.waitCard=====");
        var totalCar = this.getCardTotalNum(tb);
        logger.debug("sendNewCard=====总牌数:" + totalCar);
        logger.debug("sendNewCard=====剩余牌:" + (totalCar - tData.cardNext));
        if (tData.cardNext < totalCar) {
            logger.debug("sendNewCard=====cardNext=====" + tData.cardNext);

            var newCard;
            if (tData.mofanpigupai && tData.checkfanpiguCard) {
                console.log("玩家选择了翻屁股牌后的发牌，翻屁股请求消息", tData);

                newCard = tData.checkfanpiguCard;
                tData.mofanpigupai = false; // 重置标志
                tData.checkfanpiguCard = null; // 清空选择
            } else {
                newCard = cards[tData.cardNext++];
            }
            // 记录发牌和剩余牌堆
            var remainCards = tb.cards.slice(tData.cardNext);
            cloneDataAndPush(tb.mjlog, "logCardHeap", {
                action: "sendCard",
                card: newCard,
                remainCards: remainCards,
                uid: uid
            });
            if (tData.putType == 0 || tData.putType == 4) {
                tData.curPlayer = (tData.curPlayer + 1) % tData.maxPlayer;
            }

            if (tData.curPlayer == tData.zhuang) {
                tData.sendCardCountOfZhuang += 1;
            }

            var uid = tData.uids[tData.curPlayer];
            var pl = tb.getPlayer(uid);
            pl.mjhand.push(newCard);
            pl.newSendCard = newCard;
            pl.skipPeng = [];
            pl.skipHu = [];
            pl.isNew = true;
            tData.tState = TableState.waitPut;// 发牌了,那么裁判状态为等待出牌
            tb.AllPlayerRun(function (p) {
                p.mjState = TableState.waitPut;
                p.eatFlag = 0;
            }.bind(this));

            if (this.getHuType(tb, pl, 0, tData.hunCard)) {
                pl.eatFlag = 8;
            }

            pl.notify("newCard", { newCard: newCard, eatFlag: pl.eatFlag });
            cloneDataAndPush(tb.mjlog, "logNewCard", { newCard: newCard, eatFlag: pl.eatFlag }, { uid: pl.uid });//发牌

            tb.NotifyAll("waitPut", tData);
            cloneDataAndPush(tb.mjlog, "waitPut", tData);
            tb.doTrust(pl);

            //托管 > 自动摸打
            if (!pl.trust && pl.tPutCard && pl.eatFlag != 8
                && this.majiang.canGang1(tData, pl.mjpeng, pl.mjhand, pl.isTing).length == 0) {
                // 听牌后自动出牌
                if (pl.autoTimer) {
                    clearTimeout(pl.autoTimer);
                }
                pl.autoTimer = setTimeout(function () {
                    pl.autoTimer = null;
                    var outCard = pl.mjhand[pl.mjhand.length - 1]
                    if (pl.mjState == TableState.waitPut && outCard != tData.hunCard) {
                        g_this.mjPutCard(pl, { cmd: "MJPut", card: outCard }, null, null, tb);
                    }
                }, 1000 * 0.5);
            }
            return true;
        }
        else {
            //流局时记录剩余牌堆
            var remainCards = tb.cards.slice(tData.cardNext);
            cloneDataAndPush(tb.mjlog, "logCardHeap", {
                action: "huangZhuang",
                remainCards: remainCards
            });
            //没有牌了
            logger.debug("sendNewCard=====没牌,黄庄 endGame=====");
            this.EndGame(tb, null);
        }
    }
    return false;
};

//自动摸打信息
GameCodeGeJiuMJ.prototype.mjTouchPutCard = function (pl, msg, session, next, tb) {
    if (pl.autoTimer) {
        clearTimeout(pl.autoTimer);
        pl.autoTimer = null;
    }
    var tData = tb.tData;
    pl.tPutCard = msg.tPutCard;
    msg = { tPutCard: pl.tPutCard, uid: pl.uid };
    pl.notify("MJTouchPutCard", msg);
    cloneDataAndPush(tb.mjlog, "MJTouchPutCard", msg);
};

//自动托管出牌
GameCodeGeJiuMJ.prototype.trustPutCard = function (pl, tb) {
    var tData = tb.tData;
    if (pl.eatFlag & 8) {
        this.mjHuCard(pl, { eatFlag: pl.eatFlag }, null, null, tb);
    }
    else if (tData.canYangNum > 0) {
        pl.trust = false;
        pl.clearTrustTimer();
        tb.NotifyAll('cancelTrust', { uid: pl.uid });
        tb.AllPlayerRun(function (p) {
            if (p.yangStatus <= 0) {
                return;
            }

            this.MJYangCard(p, { isYang: false }, null, null, tb);
        });
    }
    else if (pl.mjState == TableState.waitPut && pl.uid == tData.uids[tData.curPlayer]) {
        var lastCard = pl.mjhand[pl.mjhand.length - 1];
        this.mjPutCard(pl, { cmd: "MJPut", card: lastCard }, null, null, tb);
    }
    else if (pl.mjState == TableState.waitEat) {
        this.mjPassCard(pl, { eatFlag: pl.eatFlag }, null, null, tb);
    }
};

GameCodeGeJiuMJ.prototype.checkDouDiZhu = function (tb) {
    const tData = tb.tData;
    if (!tData.douDiZhuState) return;
    console.log("当前tData的值", tData);


    const zhuangUid = tData.uids[tData.zhuang];
    let baseScore = isZiCard(tData.douDiZhuCard) ? 2 : 1; // 字牌2分，其他1分
    const xianJia = [];



    // 收集闲家信息
    tb.AllPlayerRun(pl => {
        if (pl.uid != zhuangUid) {
            xianJia.push({
                pl,
                success: pl.firstPutCard === tData.douDiZhuCard
            });
        }
    });

    // 判断是否有操作
    let hasOperation = false;
    tb.AllPlayerRun(pl => {
        if (pl.hasOperated) hasOperation = true;
    });

    if (hasOperation) {
        // 有操作本轮无效
        tb.NotifyAll("douDiZhuResult", { valid: false });
        tData.douDiZhuState = false;
        return;
    }

    // 统计结果
    const failedPlayers = xianJia.filter(p => !p.success);
    console.log("failedPlayers的值", failedPlayers);

    const successCount = xianJia.length - failedPlayers.length;
    // 结算规则
    if (tData.maxPlayer === 2) {
        // 二人局
        if (successCount === 0) {
            // 闲家失败
            const xianPl = xianJia[0].pl;
            xianPl.winone -= baseScore;
            tb.getPlayer(zhuangUid).winone += baseScore;
            tb.getPlayer(zhuangUid).douDiZhuScore += baseScore; // 记录斗地主得分
            console.log("斗地主结算：庄家得分", tb.getPlayer(zhuangUid).winone);
            console.log("斗地主结算：闲家得分", xianPl.winone);

            // 继续下一圈
            tData.douDiZhuState = true;
            tData.douDiZhuRound += 1;
            tb.AllPlayerRun(pl => {
                if (pl.uid != zhuangUid) {
                    pl.isFirstPut = true;
                    pl.firstPutCard = null;
                } else {
                    pl.isFirstPut = true;
                    pl.firstPutCard = null;
                }
            });
            tb.NotifyAll("douDiZhuNextRound", { round: tData.douDiZhuRound });
            return;
        } else {
            // 闲家成功
            const xianPl = xianJia[0].pl;
            tb.getPlayer(zhuangUid).winone -= baseScore;
            tb.getPlayer(zhuangUid).douDiZhuScore -= baseScore; // 记录斗地主得分
            xianPl.winone += baseScore;
            xianPl.douDiZhuScore += baseScore; // 记录斗地主得分
        }
    } else if (tData.maxPlayer === 3) {
        // 三人局
        if (failedPlayers.length === 1) {
            // 一个失败
            failedPlayers[0].pl.winone -= baseScore * 2;
            tb.getPlayer(zhuangUid).winone += baseScore;
            xianJia.filter(p => p.success).forEach(p => p.pl.winone += baseScore);

            //记录斗地主得分
            failedPlayers[0].pl.douDiZhuScore -= baseScore * 2;
            tb.getPlayer(zhuangUid).douDiZhuScore += baseScore;
            xianJia.filter(p => p.success).forEach(p => p.pl.douDiZhuScore += baseScore);

        } else if (failedPlayers.length === 0) {
            // 全部成功
            tb.getPlayer(zhuangUid).winone -= baseScore * 2;
            xianJia.forEach(p => p.pl.winone += baseScore);

            //斗地主得分
            tb.getPlayer(zhuangUid).douDiZhuScore -= baseScore * 2;
            xianJia.forEach(p => p.pl.douDiZhuScore += baseScore);
        } else if (failedPlayers.length === 2) {
            // 两个人不成功继续斗
            tData.douDiZhuState = true;
            tData.douDiZhuRound += 1;
            tb.AllPlayerRun(pl => {
                if (pl.uid != zhuangUid) {
                    pl.isFirstPut = true;
                    pl.firstPutCard = null;
                } else {
                    pl.isFirstPut = true;
                    pl.firstPutCard = null;
                }
            });
            tb.NotifyAll("douDiZhuNextRound", { round: tData.douDiZhuRound });
            return;
        }
    } else if (tData.maxPlayer === 4) {
        // 四人局
        if (failedPlayers.length === 0) {
            const fanbei = tData.areaSelectMode.doudizhufanbei;
            if (fanbei) {
                baseScore *= 2; // 大地主翻倍
            }
            // 大地主
            tb.getPlayer(zhuangUid).winone -= baseScore * 6;
            xianJia.forEach(p => p.pl.winone += baseScore * 2);

            //记录大地主得分
            tb.getPlayer(zhuangUid).daDiZhuScore -= baseScore * 6; // 大地主得分
            xianJia.forEach(p => {
                p.pl.douDiZhuScore += baseScore * 2; // 斗地主得分
            });
        } else if (failedPlayers.length === 1) {
            // 一个失败
            failedPlayers[0].pl.winone -= baseScore * 3;
            tb.getPlayer(zhuangUid).winone += baseScore;
            xianJia.filter(p => p.success).forEach(p => p.pl.winone += baseScore);

            //记录大地主得分和斗地主得分
            failedPlayers[0].pl.douDiZhuScore -= baseScore * 3;
            tb.getPlayer(zhuangUid).douDiZhuScore += baseScore; // 斗地主得分
            xianJia.filter(p => p.success).forEach(p => p.pl.douDiZhuScore += baseScore);
        } else if (failedPlayers.length === 2) {
            // 两个人不成功继续斗
            tData.douDiZhuState = true;
            tData.douDiZhuRound += 1;
            tb.AllPlayerRun(pl => {
                if (pl.uid != zhuangUid) {
                    pl.isFirstPut = true;
                    pl.firstPutCard = null;
                } else {
                    pl.isFirstPut = true;
                    pl.firstPutCard = null;
                }
            });
            tb.NotifyAll("douDiZhuNextRound", { round: tData.douDiZhuRound });
            return;
        }
    }

    // 在结算通知中添加翻倍信息
    tb.NotifyAll("douDiZhuResult", {
        valid: true,
        card: tData.douDiZhuCard,
        results: xianJia.map(p => ({
            uid: p.pl.uid,
            success: p.success,
            scoreChange: p.success ? baseScore : -baseScore // 添加分数变化
        }))
    });

    tData.douDiZhuState = false;
};
// 辅助函数：判断字牌
function isZiCard(card) {
    const ziCards = [31, 41, 51, 61, 71, 81, 91];
    return ziCards.indexOf(card);
}


//出
GameCodeGeJiuMJ.prototype.mjPutCard = function (pl, msg, session, next, tb) {
    pl.GangKaiHua = 0;
    if (pl.isTing && msg.card != pl.newSendCard) {
        logger.debug("mjPutCard=====听牌后只能打抓的牌=" + pl.newSendCard + "你打的事=" + msg.card);
        return;
    }
    var tData = tb.tData;
    logger.debug("mjPutCard=====msg.card:" + msg.card);
    logger.debug("mjPutCard=====tData.tState:" + tData.tState);
    logger.debug("mjPutCard=====tData.uids[tData.curPlayer]:" + (tData.uids[tData.curPlayer]));
    logger.debug("mjPutCard=====mjHand===========" + JSON.stringify(pl.mjhand));
    if (tData.tState != TableState.waitPut || pl.uid != tData.uids[tData.curPlayer]) {
        return;
    }

    var cdIdx = pl.mjhand.indexOf(msg.card);
    if (cdIdx < 0) {
        return;
    }

    tb.AllPlayerRun(function (pll) {
        pll.putType = 1;// 玩家出牌状态为普通
    });

    if (pl.eatFlag & 8) {//过胡
        pl.skipHu.push(msg.card);
    }

    if (pl.uid == tData.uids[tData.zhuang] && pl.isFirstPut && tData.areaSelectMode.doudizhu) {
        tData.douDiZhuCard = msg.card; // 记录庄家第一张牌
        tData.douDiZhuState = true; // 开始斗地主流程
        tData.douDiZhuRound = (tData.douDiZhuRound || 0) + 1; // 记录圈数
        pl.isFirstPut = false;
    }

    if (tData.douDiZhuState && pl.uid != tData.uids[tData.zhuang]) {
        // 首次出牌且未操作
        if (pl.isFirstPut && !pl.hasOperated) {
            pl.isFirstPut = false;
            pl.firstPutCard = msg.card;

            // 检查是否所有闲家都已出牌
            let allPlayed = true;
            tb.AllPlayerRun(p => {
                if (p.uid != tData.uids[tData.zhuang] && p.isFirstPut) {
                    allPlayed = false;
                }
            });

            if (allPlayed) {
                this.checkDouDiZhu(tb); // 结算斗地主
            }
        }
    }
    //庄家首次出牌
    if (pl.uid == tData.uids[tData.zhuang] && tData.firstPutCardOfZhuang == 0 && pl.isFirstPut) {
        tData.firstPutCardOfZhuang = msg.card;
        pl.isGenZhuang = true;
        pl.isFirstPut = false;
    }

    //非庄家跟庄
    if (pl.uid != tData.uids[tData.zhuang] && tData.firstPutCardOfZhuang != 0 && msg.card == tData.firstPutCardOfZhuang && pl.isFirstPut && tData.sendCardCountOfZhuang == 1) {
        pl.isGenZhuang = true;
        pl.firstPutCard = msg.card; // 记录玩家第一次出牌
        pl.isFirstPut = false;
    }

    var genZhuangState = false;

    if (tb.AllPlayerCheck((p) => {
        return p.isGenZhuang;
    }) && tData.sendCardCountOfZhuang == 1 && tData.firstPutCardOfZhuang == msg.card) {
        genZhuangState = true;
    }

    msg.genZhuangState = (genZhuangState && (tData.maxPlayer > 2));

    if (msg.genZhuangState) {
        msg.genZhuangScore = {};
        var difen = tData.areaSelectMode["difen"] || 1;
        tb.AllPlayerRun(function (p) {
            if (p.uid != tData.uids[tData.zhuang]) {
                p.genZhuangScore = revise(difen);
            } else {
                p.genZhuangScore -= revise(difen * (tData.maxPlayer - 1));
            }
            msg.genZhuangScore[p.uid] = p.genZhuangScore;
        });
    }

    pl.mjhand.splice(cdIdx, 1);
    msg.uid = pl.uid;
    tData.lastPutCard = msg.card;
    tData.putType = 0;
    tData.lastPutPlayer = tData.curPlayer;
    tData.tState = TableState.waitEat;
    pl.mjState = TableState.waitCard;
    pl.eatFlag = 0;//自己不能吃
    var eatFlags = {};
    eatFlags[pl.uid] = 0;
    var t_this = this;
    var isFlower = this.isHardFlower(msg.card);
    logger.debug("mjPutCard=====isFlower=" + isFlower);
    if (isFlower) {
        logger.debug("mjPutCard=====通知客户端补花了=======");
        pl.mjflower.push(msg.card);
        pl.putType = 5;
        tData.putType = 5;//花牌
        tb.NotifyAll("MJFlower", { uid: pl.uid, card: msg.card });
        cloneDataAndPush(tb.mjlog, "MJFlower", { uid: pl.uid, card: msg.card });
        tb.AllPlayerRun(function (pl) {
            pl.mjState = TableState.waitCard;
        });
    }
    else {
        pl.mjput.push(msg.card);

        if (pl.isGang == 0) {
            tb.AllPlayerRun(function (p) {
                p.isGang = 0;
            });
        }


        // 十老头计数逻辑
        var ziCards = [31, 41, 51, 61, 71, 81, 91];
        var isZiCard = ziCards.indexOf(msg.card) != -1;

        if (isZiCard) {
            // 如果上一次出牌是字牌，则连续计数加1
            if (pl.lastPutCard && ziCards.indexOf(pl.lastPutCard) != -1) {
                pl.consecutiveZiPutCount++;
                pl.consecutiveZiPutCards.push(msg.card);
            } else {
                // 否则重新开始计数
                pl.consecutiveZiPutCount = 1;
                pl.consecutiveZiPutCards = [msg.card];
            }

            // 检查是否达到第十张字牌
            if (pl.consecutiveZiPutCount >= 10) {
                // 设置十老头状态
                pl.isShiLaoTou = true;

                // 直接触发胡牌
                this.mjHuCard(pl, { eatFlag: 8 }, null, null, tb);
                return; // 不再继续后续发牌逻辑
            }
        } else {
            // 打出非字牌重置计数
            pl.consecutiveZiPutCount = 0;
            pl.consecutiveZiPutCards = [];
        }

        // 记录当前出牌
        pl.lastPutCard = msg.card;

        tb.AllPlayerRun(function (p) {
            if (p != pl) {
                p.eatFlag = t_this.getEatFlag(tb, p);
                if (tData.areaSelectMode.onlyZiMoHu && (p.eatFlag & 8)) {
                    p.eatFlag -= 8;
                }
                eatFlags[p.uid] = p.eatFlag;
                if (p.eatFlag != 0) {
                    p.mjState = TableState.waitEat;
                    tb.doTrust(p);
                }
                else {
                    p.mjState = TableState.waitCard;
                }
            }
            else {
                p.mjState = TableState.waitCard;
            }
        });

        if (msg.tingAfterPut) {
            if (this.majiang.canTing(pl.mjhand)) {
                pl.isTing = true;
                pl.putCardAfterTing = msg.card;
                tb.NotifyAll("MJTing", { uid: pl.uid, putCardAfterTing: pl.putCardAfterTing });
                cloneDataAndPush(tb.mjlog, "MJTing", { uid: pl.uid, putCardAfterTing: pl.putCardAfterTing });
                logger.debug("MJTing=====通知客户端玩家听了==pl.uid=====" + pl.uid);
            }
            else {
                logger.debug("MJTing=====听牌失败了看看是不是能听======");
            }
        }

        logger.debug("mjPutCard=====mjHand 3======" + JSON.stringify(pl.mjhand));
        var cmd = msg.cmd;
        delete msg.cmd;
        logger.debug("mjPutCard=====玩家出牌了======");
        msg.putType = tData.putType;
        msg.eatFlags = eatFlags;
        logger.debug("mjPutCard=====cmd=====" + cmd + "     msg:" + JSON.stringify(msg));
        tb.NotifyAll(cmd, msg);
        cloneDataAndPush(tb.mjlog, cmd, msg);
    }


    // 塘子小七对记录逻辑
    pl.tangZiPutCards.push(msg.card);

    // 检查是否达到13张牌
    if (pl.tangZiPutCards.length > 13) {
        pl.tangZiPutCards.shift(); // 保持最多13张牌
    }

    // 检查塘子牌型
    if (pl.tangZiPutCards.length >= 13) {
        const tangZiType = this.majiang.isTangZiLanPai(pl.tangZiPutCards);
        if (tangZiType > 0) {
            pl.eatFlag = 8;
            pl.tangZiType = tangZiType;
            this.mjHuCard(pl, { eatFlag: 8 }, null, null, tb);
            return;
        }

        // 检查塘子小七对
        if (this.majiang.isTangZiQiDui(pl.tangZiPutCards)) {
            pl.isTangZiQiDui = true;
            this.mjHuCard(pl, { eatFlag: 8 }, null, null, tb);
            return;
        }
    }


    logger.debug("mjPutCard=====xcl test");
    tData.saveActions = [];

    this.sendNewCard(tb);

}

GameCodeGeJiuMJ.prototype.checkFinishRound = function (tb, pl) { // 判断过吃碰杠操作是否继续
    logger.debug("mjPutCard=====checkFinishRound");
    if (!tb.tData.areaSelectMode["duoHu"]) {
        return false; // 截胡玩法中"过吃碰杠"不会导致游戏结束
    }
    if (tb.CheckPlayerCount(function (p) {
        return p.mjState == TableState.roundFinish;
    }) > 0) {// 存在已经胡的玩家
        if (this.highPlayerHu(tb, pl)) { // 还有玩家未操作
            pl.mjState = TableState.roundFinish;
            pl.notify("MJPass", { mjState: pl.mjState });
            logger.debug("mjPutCard=====checkFinishRound  还有玩家未操作");
        }
        else {
            this.EndGame(tb, pl); // 一炮多响情况下，最后操作的那个人不胡，"过吃碰杠"也应结束游戏
            logger.debug("mjPutCard=====checkFinishRound  游戏结束");
        }
        return true;
    }
    logger.debug("mjPutCard=====checkFinishRound false");
    return false;
}

//过
GameCodeGeJiuMJ.prototype.mjPassCard = function (pl, msg, session, next, tb) {
    //logger.debug("======================GameCodeGeJiuMJ.prototype.mjPassCard======================");
    var tData = tb.tData;
    logger.debug("mjPassCard========tData.tState:" + tData.tState);
    logger.debug("mjPassCard========pl.mjState:" + pl.mjState);
    if (tData.tState == TableState.waitPut && pl.mjState == TableState.waitPut) {
        cloneDataAndPush(tb.mjlog, "MJPass", { mjState: pl.mjState }, { uid: pl.uid });
    }
    else if (tData.tState == TableState.waitEat && pl.mjState == TableState.waitEat) {
        if (this.checkFinishRound(tb, pl))
            return;
        if (pl.eatFlag == msg.eatFlag) {
            logger.debug("------------mjPassCard ---");
            pl.mjState = TableState.waitCard;

            if (pl.eatFlag & 2) {
                pl.skipPeng.push(tData.lastPutCard);
            }

            if (pl.eatFlag & 8) {
                pl.skipHu.push(tData.lastPutCard);
            }

            pl.eatFlag = 0;
            var sendMsg = {
                mjState: pl.mjState,
                skipPeng: pl.skipPeng,
                skipHu: pl.skipHu
            };

            if (!pl.trust && pl.tPutCard) {
                sendMsg.touchCard = pl.mjhand[pl.mjhand.length - 1];
            }

            pl.notify("MJPass", sendMsg);
            cloneDataAndPush(tb.mjlog, "MJPass", sendMsg, { uid: pl.uid });
            tData.saveActions.sort(function (a, b) { return b.eatFlag - a.eatFlag; });
            for (var i = 0; i < tData.saveActions.length; i++) {
                var action = tData.saveActions[i];
                if (action.eatFlag <= msg.eatFlag) {
                    tData.saveActions = [];
                    action.actionFunc();
                    break;
                }
            }

            this.sendNewCard(tb);
        }
    }
    else if (tData.tState == TableState.roundFinish && pl.mjState == TableState.roundFinish) {
        pl.mjState = TableState.isReady;
        tb.NotifyAll('onlinePlayer', { uid: pl.uid, onLine: true, mjState: pl.mjState });
        pl.eatFlag = 0;
        tb.startGame();
    }
};

// 吃
GameCodeGeJiuMJ.prototype.mjChiCard = function (pl, msg, session, next, tb) {
    logger.debug("======================mjChiCard======================");
    if (pl.isTing) {
        logger.debug("mjChiCard============pl.isTing()===不能吃牌=======" + pl.isTing);
        return;
    }
    var tData = tb.tData;
    pl.tangZiPutCards = []; // 重置塘子小七对记录
    pl.hasOperated = true;
    logger.debug("mjChiCard=====tData.tState=====" + tData.tState);
    logger.debug("mjChiCard=====pl.mjState========" + pl.mjState);
    logger.debug("mjChiCard=====tData.uids[tData.curPlayer]======" + tData.uids[tData.curPlayer]);
    logger.debug("mjChiCard=====pl.uid===========" + pl.uid);
    logger.debug("mjChiCard=====tData.uids[(tData.curPlayer+1)%tData.maxPlayer]=========" + tData.uids[(tData.curPlayer + 1) % tData.maxPlayer]);
    if (tData.tState == TableState.waitEat && pl.mjState == TableState.waitEat &&
        tData.uids[tData.curPlayer] != pl.uid &&
        tData.uids[(tData.curPlayer + 1) % tData.maxPlayer] == pl.uid //下家限制
    ) {
        if (this.checkFinishRound(tb, pl))
            return;
        logger.debug("==================mjChiCard==================== msg = ", JSON.stringify(msg));
        logger.debug("==================mjChiCard====================");
        //此处必须保证没有其他玩家想 胡牌 碰牌 杠牌
        if (tb.AllPlayerCheck(function (p) {
            if (p == pl) {
                return true;
            }
            logger.debug("================== p.uid ==================== ", p.uid);
            logger.debug("================== p.eatFlag ==================== ", p.eatFlag);
            return p.eatFlag == 0;
        })) {
            var cd0 = tData.lastPutCard;
            var cd1 = tData.lastPutCard;
            if (msg.pos == 0) {
                cd0 += 1;
                cd1 += 2;
            }
            else if (msg.pos == 1) {
                cd0 -= 1;
                cd1 += 1;
            }

            else {
                cd0 -= 2;
                cd1 -= 1;
            }

            var hand = pl.mjhand;
            var idx0 = hand.indexOf(cd0);
            var idx1 = hand.indexOf(cd1);
            if (idx0 >= 0 && idx1 >= 0) {
                logger.debug("mjChiCard=====idx0=====" + idx0);
                // 如果手里有这两张牌
                hand.splice(idx0, 1);
                idx1 = hand.indexOf(cd1);
                hand.splice(idx1, 1);
                var eatCards = [cd0, cd1, tData.lastPutCard];
                eatCards.sort(function (a, b) {
                    return a - b;
                });
                logger.debug("mjChiCard=====hand=====" + JSON.stringify(hand));
                logger.debug("mjChiCard=====eatCards=====" + JSON.stringify(eatCards));

                pl.mjchi = pl.mjchi.concat(eatCards);
                pl.mjchiCard.push(tData.lastPutCard);
                pl.isNew = false;

                pl.pengchigang["chi"].push({ pos: tData.lastPutPlayer, card: tData.lastPutCard });

                var lastPlayer = tData.curPlayer;
                var putCardPl = tb.getPlayer(tData.uids[lastPlayer]);
                putCardPl.mjput.length = putCardPl.mjput.length - 1;
                logger.debug("mjChiCard=====putCardPl.mjput=====" + JSON.stringify(putCardPl.mjput));
                tData.curPlayer = tData.uids.indexOf(pl.uid);
                tData.tState = TableState.waitPut;

                tb.AllPlayerRun(function (p) {
                    p.mjState = TableState.waitPut;
                    p.eatFlag = 0;
                });

                //吃碰杠
                msg.cpginfo =
                {
                    id: pl.uid,
                    pengchigang: pl.pengchigang
                };

                var chiMsg =
                {
                    mjchi: eatCards,
                    mjchiCard: pl.mjchiCard,
                    tData: JSON.parse(JSON.stringify(tData)),
                    pos: msg.pos,
                    from: lastPlayer,
                    eatFlag: msg.eatFlag,
                    cpginfo: msg.cpginfo
                };
                logger.debug("mjChiCard=====chiMsg=====" + JSON.stringify(chiMsg));

                tb.NotifyAll('MJChi', chiMsg);
                cloneDataAndPush(tb.mjlog, "MJChi", chiMsg);
                tb.doTrust(pl);
            }
        }
        else {
            var _this = this;
            var actionFunc = (function () {
                return function () {
                    _this.mjChiCard(pl, msg, session, next, tb);
                }
            })();
            tData.saveActions.push({ eatFlag: 1, actionFunc: actionFunc });
            logger.debug("mjChiCard=====已通知玩家等待uid:" + pl.uid);
            tb.NotifyAll("loadOther", { uids: [pl.uid] });
        }
    }
};

// 碰
GameCodeGeJiuMJ.prototype.mjPengCard = function (pl, msg, session, next, tb) {
    if (pl.isTing) {
        logger.debug("mjPengCard=====pl.isTing()=====不能碰牌======" + pl.isTing);
        return;
    }
    //logger.debug("======================GameCodeGeJiuMJ.prototype.mjPengCard======================");
    var tData = tb.tData;
    pl.hasOperated = true;
    pl.tangZiPutCards = []; // 重置塘子小七对记录
    logger.debug("mjPengCard=====tData.tState=====" + tData.tState);
    logger.debug("mjPengCard=====tData.uids[tData.curPlayer]=====" + tData.uids[tData.curPlayer]);
    logger.debug("mjPengCard=====pl.uid=====" + pl.uid);
    if (
        tData.tState == TableState.waitEat && pl.mjState == TableState.waitEat &&
        tData.uids[tData.curPlayer] != pl.uid) {
        if (this.checkFinishRound(tb, pl))
            return;
        logger.debug("mjPengCard=====11111=====");
        //此处必须保证没有其他玩家想胡牌
        if (tb.AllPlayerCheck(function (p) {
            if (p == pl) {
                return true;
            }
            return p.eatFlag < 8;
        })) {
            logger.debug("mjPengCard=====22222=====");
            var hand = pl.mjhand;
            var matchnum = 0;
            for (var i = 0; i < hand.length; i++) {
                if (hand[i] == tData.lastPutCard) {
                    matchnum++;
                }
            }
            if (matchnum >= 2) {
                // 碰
                hand.splice(hand.indexOf(tData.lastPutCard), 1);
                hand.splice(hand.indexOf(tData.lastPutCard), 1);
                pl.mjpeng.push(tData.lastPutCard);
                pl.pengchigang["peng"].push({ pos: tData.lastPutPlayer, card: tData.lastPutCard });
                pl.openDoorState = true;
                if (matchnum == 3) {
                    pl.mjpeng4.push(tData.lastPutCard);
                }
                pl.isNew = false;
                pl.tPutCard = false;
                var lastPlayer = tData.curPlayer;
                var putCardPl = tb.getPlayer(tData.uids[lastPlayer]);
                putCardPl.mjput.length = putCardPl.mjput.length - 1;

                tData.curPlayer = tData.uids.indexOf(pl.uid);
                tb.AllPlayerRun(function (p) {
                    p.mjState = TableState.waitPut;
                    p.eatFlag = 0;
                });
                tData.tState = TableState.waitPut;

                //吃碰杠
                msg.cpginfo =
                {
                    id: pl.uid,
                    openDoorState: pl.openDoorState,
                    pengchigang: pl.pengchigang
                };
                tb.NotifyAll('MJPeng',
                    {
                        tData: tData,
                        from: lastPlayer,
                        cpginfo: msg.cpginfo
                    });
                cloneDataAndPush(tb.mjlog, 'MJPeng', {
                    tData: tData,
                    from: lastPlayer,
                    eatFlag: msg.eatFlag,
                    cpginfo: msg.cpginfo
                });//碰
                tb.doTrust(pl);
            }
        }
        else {
            var _this = this;
            var actionFunc = (function () {
                return function () {
                    _this.mjPengCard(pl, msg, session, next, tb);
                }
            })();
            tData.saveActions.push({ eatFlag: 2, actionFunc: actionFunc });
            logger.debug("mjPengCard=====已通知玩家等待uid:" + pl.uid);
            tb.NotifyAll("loadOther", { uids: [pl.uid] });
        }
    }
}

//杠
GameCodeGeJiuMJ.prototype.mjGangCard = function (pl, msg, session, next, tb) {
    var tData = tb.tData;
    var fanpiguCards = tData.fanpiguCards; // 获取翻屁股牌
    console.log("杠中翻屁股牌", fanpiguCards);



    pl.tangZiPutCards = []; // 重置塘子小七对记录
    pl.hasOperated = true;
    pl.GangKaiHua += 1;
    logger.debug("mjGangCard=====tData.tState=====" + tData.tState);
    logger.debug("mjGangCard=====msg=====" + JSON.stringify(msg));
    logger.debug("mjGangCard=====tData.uids[tData.curPlayer]=====" + tData.uids[tData.curPlayer]);
    logger.debug("mjGangCard=====pl.uid=====" + pl.uid);


    if (tData.tState == TableState.waitEat && pl.mjState == TableState.waitEat && tData.uids[tData.curPlayer] != pl.uid && this.checkFinishRound(tb, pl))
        return;
    if (
        (
            //吃牌杠
            tData.tState == TableState.waitEat && pl.mjState == TableState.waitEat && tData.uids[tData.curPlayer] != pl.uid
            //此处必须保证没有其他玩家想胡牌
            && (
                tb.AllPlayerCheck(function (p) {
                    if (p == pl) return true;
                    return p.eatFlag < 8;
                })
            )
            //自摸牌杠
            || tData.tState == TableState.waitPut && pl.mjState == TableState.waitPut && tData.uids[tData.curPlayer] == pl.uid
        )
    ) {
        logger.debug("mjGangCard=====11111=====");
        var hand = pl.mjhand;
        var handNum = 0;
        var putCardPl = null;
        for (var i = 0; i < hand.length; i++) {
            if (hand[i] == msg.card) {
                handNum++;
            }
        }

        if (tData.tState == TableState.waitEat && handNum == 3 && tData.lastPutCard == msg.card) {
            logger.debug("mjGangCard=====明杠=====11111=====");
            // 自己有三张，杠别人的
            putCardPl = tb.getPlayer(tData.uids[tData.curPlayer]);
            var mjput = putCardPl.mjput;
            if (mjput.length > 0 && mjput[mjput.length - 1] == msg.card) {
                mjput.length = mjput.length - 1;
            }
            else {
                return;
            }
            logger.debug("mjGangCard=====明杠=====22222=====");
            //记录点杠
            pl.mjgang0.push(msg.card);//吃明杠
            //结算界面明杠数+1
            pl.minggangTotal += 1;
            hand.splice(hand.indexOf(msg.card), 1);
            hand.splice(hand.indexOf(msg.card), 1);
            hand.splice(hand.indexOf(msg.card), 1);

            msg.gang = 1;
            msg.from = tData.curPlayer;
            pl.isNew = false;
            pl.pengchigang["gang"].push({ pos: tData.lastPutPlayer, card: tData.lastPutCard });
            logger.debug("mjGangCard=====明杠=====33333=====");
        }
        else if (tData.tState == TableState.waitPut && handNum == 4) {
            logger.debug("mjGangCard=====暗杠=====11111=====");
            console.log("摸到的杠");

            // 暗杠
            pl.mjgang1.push(msg.card);

            //结算界面暗杠数+1
            pl.angangTotal += 1;

            hand.splice(hand.indexOf(msg.card), 1);
            hand.splice(hand.indexOf(msg.card), 1);
            hand.splice(hand.indexOf(msg.card), 1);
            hand.splice(hand.indexOf(msg.card), 1);
            msg.gang = 3;
        }

        else if (tData.tState == TableState.waitPut && handNum == 1 && pl.mjpeng.indexOf(msg.card) >= 0) {
            //自摸明杠
            logger.debug("mjGangCard=====自摸明杠=====11111=====");
            //找出点碰的人
            var pengPos = tData.lastPutPlayer;
            var pengList = pl.pengchigang["peng"];
            for (var t = 0; t < pengList.length; t++) {
                var item = pengList[t];
                if (item.card == msg.card) {
                    pengPos = item.pos;
                    pengList.splice(t, 1);
                }
            }
            pl.pengchigang["pgang"].push({ pos: pengPos, card: msg.card });
            pl.mjgang0.push(msg.card);

            //结算界面明杠数+1
            pl.minggangTotal += 1;
            hand.splice(hand.indexOf(msg.card), 1);
            pl.mjpeng.splice(pl.mjpeng.indexOf(msg.card), 1);
            msg.gang = 2;
        }
        else {
            return;
        }

        msg.gang = msg.gang || 0;
        if (msg.gang > 0) { // 有人杠了，全部重置一遍
            tb.AllPlayerRun(function (p) {
                p.isGang = 0;
            });
        }

        msg.uid = pl.uid;
        //判断是否有抢杠胡
        var canRobGangHu = 0;
        pl.putType = 4;
        pl.tPutCard = false;
        var t_this = this;
        // logger.debug("tData.playType:"+tData.playType);
        logger.debug("aaaa");
        var eatFlags = {};
        for (var i = 0; i < tData.maxPlayer; i++) {
            var p = tb.players[tData.uids[(tData.curPlayer + i) % tData.maxPlayer]];
            // 抢杠
            p.mjState = TableState.waitCard;
            p.eatFlag = 0;
            if (msg.gang == 2 && p != pl && tData.areaSelectMode["canRob"]) {
                // 只有在自摸明杠的时候才能抢杠胡
                //选择点炮胡才能抢杠
                var huState = t_this.getHuType(tb, p, msg.card, tData.hunCard) && (p.skipHu.indexOf(msg.card) == -1);
                if (huState
                    && this.majiang.canRob(p.mjhand, tData.hunCard, tb)
                    && (tData.areaSelectMode["duoHu"] || canRobGangHu == 0))//抢杠胡
                {
                    canRobGangHu++;
                    p.mjState = TableState.waitEat;
                    p.eatFlag = 8;
                    tb.doTrust(p);
                }
            }
            eatFlags[p.uid] = p.eatFlag;
            logger.debug("mjGangCard=====p.mjState:" + p.mjState);
        };
        logger.debug("bbbb");
        tData.tState = TableState.waitEat;

        pl.isGang = msg.gang;

        if (canRobGangHu > 0) {
            // 抢杠胡
            tData.putType = 2;
            tData.curPlayer = tData.uids.indexOf(pl.uid);
            tData.lastPutCard = msg.card;
            logger.debug("cccc");
        }
        else {
            tData.putType = 0;
            tData.curPlayer = (tData.uids.indexOf(pl.uid) + (tData.maxPlayer - 1)) % tData.maxPlayer;
            logger.debug("dddd");
        }

        //吃碰杠
        msg.cpginfo =
        {
            id: pl.uid,
            pengchigang: pl.pengchigang,
            openDoorState: pl.openDoorState,
        };
        logger.debug("eeee");
        msg.eatFlags = eatFlags;
        //杠后当前状态为杠
        tb.NotifyAll('GJGang', msg);
        cloneDataAndPush(tb.mjlog, 'GJGang', msg);

        logger.debug("mjGangCard=====通知客户端扛了=====");
        if (tData.putType != 2) {
            if (tData.areaSelectMode.fanpigu && tData.fanpiguCards.length > 0) {
                // 发送翻屁股请求给杠牌玩家
                tData.mofanpigupai = true;
                console.log("当前玩家杠牌后，翻屁股请求消息", msg);

                tb.NotifyAll('sendCard', msg);
            } else {
                this.sendNewCard(tb);
            }
        }
    }
    else {
        var _this = this;
        var actionFunc = (function () {
            return function () {
                _this.mjGangCard(pl, msg, session, next, tb);
            }
        })();
        tData.saveActions.push({ eatFlag: 4, actionFunc: actionFunc });
        logger.debug("mjGangCard=====已通知玩家等待uid:" + pl.uid);
        tb.NotifyAll("loadOther", { uids: [pl.uid] });
    }
}

GameCodeGeJiuMJ.prototype.getFanPiGuCard = function (pl, msg, session, next, tb) {
    var tData = tb.tData;

    console.log("当前玩家选择了要摸翻屁股牌，翻屁股请求消息", msg);
    // 1. 从翻屁股牌堆中移除玩家选择的牌
    var cardIndex = tData.fanpiguCards.indexOf(msg.card);
    if (cardIndex !== -1) {
        tData.fanpiguCards.splice(cardIndex, 1);
    }

    // 2. 记录翻屁股牌的变化
    cloneDataAndPush(tb.mjlog, 'fanpigu', {
        action: "sendCard",
        selectedCard: msg.card,
        remainCards: tData.fanpiguCards.slice()
    });

    // 3. 补充新的翻屁股牌（如果牌堆足够，且翻屁股牌不足2张）
    while (tData.fanpiguCards.length < 2 && tb.cards.length > 0) {
        var newCard = tb.cards.pop();
        tData.fanpiguCards.push(newCard);
        cloneDataAndPush(tb.mjlog, 'fanpigu', {
            action: "sendCard",
            newCards: [newCard],
            remainCards: tData.fanpiguCards.slice()
        });
    }

    // 4. 发送翻屁股牌更新消息给所有客户端
    var updateMsg = {
        fanpiguCards: tData.fanpiguCards.slice(),
        selectedCard: msg.card,
        uid: pl.uid
    };
    tb.NotifyAll("updateFanPiGuCards", updateMsg);
    cloneDataAndPush(tb.mjlog, "updateFanPiGuCards", updateMsg);

    // 5. 设置要摸的牌并触发发牌
    tData.checkfanpiguCard = msg.card;
    this.sendNewCard(tb);
};

GameCodeGeJiuMJ.prototype.highPlayerHu = function (tb, pl) {
    var g_this = this;
    if (tb.CheckPlayerCount(function (p) {
        logger.debug("highPlayerHu=====p.mjState:" + p.mjState + ", eatFlag = " + p.eatFlag);
        if (pl.uid != p.uid && p.mjState == TableState.waitEat && p.eatFlag >= 8) {
            return true;
        }
        return false;
    }) > 0) {
        logger.debug("highPlayerHu=====HighPlayerHu: return true=====");
        return true;
    }
    logger.debug("highPlayerHu=====HighPlayerHu: return false=====");
    return false;
}

GameCodeGeJiuMJ.prototype.mjJieHu = function (pl, tb) {
    var tData = tb.tData;
    if (pl == tb.players[tData.uids[(tData.curPlayer + 1) % tData.maxPlayer]]) {

    }
    else if (pl == tb.players[tData.uids[(tData.curPlayer + 2) % tData.maxPlayer]]) {
        var eatFlag1 = tb.players[tData.uids[(tData.curPlayer + 1) % tData.maxPlayer]].eatFlag;
        if (eatFlag1 >= pl.eatFlag || eatFlag1 >= 8) {
            return true;
        }
    }
    else if (pl == tb.players[tData.uids[(tData.curPlayer + 3) % tData.maxPlayer]]) {
        var eatFlag1 = tb.players[tData.uids[(tData.curPlayer + 1) % tData.maxPlayer]].eatFlag;
        var eatFlag2 = tb.players[tData.uids[(tData.curPlayer + 2) % tData.maxPlayer]].eatFlag;
        if (eatFlag1 >= pl.eatFlag || eatFlag1 >= 8 || eatFlag2 >= pl.eatFlag || eatFlag2 >= 8) {
            return true;
        }
    }
    return false;
}

// 胡
GameCodeGeJiuMJ.prototype.mjHuCard = function (pl, msg, session, next, tb) {
    logger.debug("======================mjHuCard======================");
    var tData = tb.tData;
    var canEnd = false;
    logger.debug("mjHuCard=====tData.tState=====" + tData.tState);
    logger.debug("mjHuCard=====pl.mjState=====" + pl.mjState);
    logger.debug("mjHuCard=====pl.isNew=====" + pl.isNew);
    logger.debug("mjHuCard=====tData.uids[tData.curPlayer]=====" + tData.uids[tData.curPlayer]);
    logger.debug("mjHuCard=====pl.mjhand=====" + pl.mjhand);


    //十老头胡牌
    if (pl.isShiLaoTou) {
        logger.debug("mjHuCard=====十老头胡牌=====");
        pl.winType = WinType.pickNormal;    //自摸
        canEnd = true;
        tData.lastPutCard = 0; // 特殊标记十老头胡牌

        // 构建胡牌消息
        var huMsg = {
            uid: pl.uid,
            eatFlag: 8,
            mjhand: pl.tangZiPutCards.slice(-10),
            huWord: "十老头胡牌", // 特殊牌型名称
            tangZiCards: pl.tangZiPutCards.slice(-10),  // 包含所有塘子牌
            specialHuType: "tangzi", // 特殊牌型标识
            tangZiType: 6  // 6代表十老头胡牌
        };

        tb.NotifyAll("MJHu", huMsg);
        cloneDataAndPush(tb.mjlog, "MJHu", huMsg);
        this.EndGame(tb, pl);
        return;
    }

    //塘子小七对胡牌特殊处理
    if (pl.isTangZiQiDui) {
        logger.debug("mjHuCard=====塘子小七对胡牌=====");
        pl.winType = WinType.pickNormal;    //自摸
        canEnd = true;
        tData.lastPutCard = 0; // 特殊标记
        console.log("塘子小七对胡牌牌型", pl.tangZiPutCards.slice());


        // 构建胡牌消息
        var huMsg = {
            uid: pl.uid,
            eatFlag: msg.eatFlag,
            mjhand: pl.tangZiPutCards.slice(),
            huWord: "塘子小七对",
            specialHuType: "tangzi",
            tangZiCards: pl.tangZiPutCards.slice(),  // 包含所有塘子牌
            tangZiType: 5  // 5代表塘子小七对
        };

        tb.NotifyAll("MJHu", huMsg);
        cloneDataAndPush(tb.mjlog, "MJHu", huMsg);
        this.EndGame(tb, pl);
        return;
    }

    // 塘子牌型胡牌特殊处理
    if (pl.tangZiType > 0) {
        logger.debug("mjHuCard=====塘子牌型胡牌=====类型:" + pl.tangZiType);
        pl.winType = WinType.pickNormal;    //自摸
        canEnd = true;
        tData.lastPutCard = 0; // 特殊标记

        // 构建胡牌消息
        let huWord = "";
        switch (pl.tangZiType) {
            case 1: huWord = "塘子烂牌"; break;
            case 2: huWord = "塘子一般高"; break;
            case 3: huWord = "塘子七星"; break;
            case 4: huWord = "塘子七星一般高"; break;
        }

        var huMsg = {
            uid: pl.uid,
            eatFlag: 8,
            mjhand: pl.mjhand,
            huWord: huWord,
            specialHuType: "tangzi",
            tangZiType: pl.tangZiType,
            tangZiCards: pl.tangZiPutCards.slice()  // 包含所有塘子牌
        };

        tb.NotifyAll("MJHu", huMsg);
        cloneDataAndPush(tb.mjlog, "MJHu", huMsg);
        this.EndGame(tb, pl);
        return;
    }


    //自摸胡
    if (
        tData.tState == TableState.waitPut &&
        pl.mjState == TableState.waitPut &&
        pl.isNew &&
        tData.uids[tData.curPlayer] == pl.uid &&
        pl.eatFlag >= 8
    ) {
        logger.debug("mjHuCard=====自摸=====");
        pl.winType = WinType.pickNormal;    //自摸
        //结算自摸胡加1
        pl.zimoTotal += 1;
        canEnd = true;
        tData.lastPutCard = pl.newSendCard;
    }
    else if (
        tData.tState == TableState.waitEat &&
        pl.mjState == TableState.waitEat &&
        tData.uids[tData.curPlayer] != pl.uid &&
        pl.eatFlag >= 8
    ) {
        if (tData.tState == TableState.waitEat) {
            logger.debug("mjHuCard=====点炮胡=====");
            //截胡
            var duoHu = tData.areaSelectMode["duoHu"];
            if (!duoHu && this.mjJieHu(pl, tb)) {
                logger.debug("mjHuCard=====有截胡玩家，请等待=======");
                logger.debug("mjHuCard=====通知客户端等待其他玩家操作");
                logger.debug("mjHuCard=====已通知玩家等待uid:" + pl.uid);
                tb.NotifyAll("loadOther", { uids: [pl.uid] });
                return;
            }
            var winType = null;
            if (tData.putType == 0 || tData.putType == 4) //点炮
            {
                winType = WinType.eatPut;
                pl.dianPaoPlayer = tData.lastPutPlayer;
                //结算界面点炮总数+1
                var dianPaoPl = tb.getPlayer(tData.uids[tData.lastPutPlayer]);
                dianPaoPl.dianpaoTotal += 1;
                //结算界面接炮总数+1
                pl.jiepaoTotal += 1;
            } else //抢杠胡tData.putType == 2
            {
                winType = WinType.eatGang;
                pl.dianPaoPlayer = tData.curPlayer;
                pl.zimoTotal += 1;//抢杠胡算自摸
                //结算界面点炮总数+1
                var robGangPl = tb.getPlayer(tData.uids[tData.curPlayer]);

                logger.debug("mjHuCard=====删除杠牌" + tData.lastPutCard);
                tb.AllPlayerRun(function (p) {
                    var pgangList = p.pengchigang["pgang"];
                    logger.debug("mjHuCard=====" + p.uid, pgangList);
                    for (var i = 0; i < pgangList.length; i++) {
                        if (pgangList[i].card == tData.lastPutCard) {
                            logger.debug("mjHuCard=====splice");
                            p.pengchigang["peng"].push(pgangList[i]);
                            pgangList.splice(i, 1);
                        }
                    }

                    var gangIndex = p.mjgang0.indexOf(tData.lastPutCard);
                    if (gangIndex >= 0) {
                        logger.debug("mjHuCard=====mjgang0");
                        p.mjpeng.push(tData.lastPutCard)
                        p.mjgang0.splice(gangIndex, 1);
                    }
                });
                logger.debug("mjHuCard=====删除杠牌 end");
            }
            logger.debug("mjHuCard=====pl.uid:" + pl.uid);
            pl.mjhand.push(tData.lastPutCard);
            pl.winType = winType;
            logger.debug("mjHuCard=====pl.mjState==TableState.waitEat=====" + (pl.mjState == TableState.waitEat));
            if (duoHu && pl.mjState == TableState.waitEat && this.highPlayerHu(tb, pl)) {
                logger.debug("mjHuCard=====一炮多胡=====");
                logger.debug("mjHuCard=====roundFinish: pl.uid=====" + pl.uid);
                tb.NotifyAll("loadOther", { uids: [pl.uid] });
                pl.mjState = TableState.roundFinish;

                var huMsg = {
                    uid: pl.uid,
                    eatFlag: msg.eatFlag,
                    mjhand: pl.mjhand,
                    huWord: this.majiang.showHuWord(tData, pl)
                };

                tb.NotifyAll("MJHu", huMsg);
                cloneDataAndPush(tb.mjlog, "MJHu", huMsg);
            }
            else {
                logger.debug("mjHuCard=====截胡=====");
                canEnd = true;
            }
        }
    }
    if (canEnd) {
        logger.debug("mjHuCard=====canEnd=====");
        var huMsg = {
            uid: pl.uid,
            eatFlag: msg.eatFlag,
            mjhand: pl.mjhand,
            huWord: this.majiang.showHuWord(tData, pl)
        };
        tb.NotifyAll("MJHu", huMsg);
        cloneDataAndPush(tb.mjlog, "MJHu", huMsg);
        this.EndGame(tb, pl);
    }
};

GameCodeGeJiuMJ.prototype.EndRoom = function (tb, msg) {
    var playInfo = null;
    if (tb.tData.roundNum > -2) {
        if (tb.tData.roundNum != tb.createParams.round) {
            var tData = tb.tData;
            playInfo = {
                gametype: tData.gameType,
                owner: tData.owner,
                money: tb.createParams.money,
                now: utils.dateFormat(new Date(), 'yyyy-MM-dd hh:mm:ss'),
                tableid: tb.tableid,
                players: []
            };
            tb.AllPlayerRun(function (p) {
                var pinfo = {};
                pinfo.uid = p.uid;
                pinfo.winall = p.winall;
                pinfo.nickname = p.info.nickname;
                pinfo.money = p.info.money;
                playInfo.players.push(pinfo);
                p.info.lastGameTime = new Date().getTime();
                modules.user.updateInfo({ userId: p.uid, lastGameTime: p.info.lastGameTime });
            });
        }
        if (msg) {
            if (playInfo) msg.playInfo = playInfo;
            if (!msg.showEnd) msg.showEnd = tb.tData.roundNum != tb.createParams.round;
            msg.players = tb.collectPlayer('lastOffLineTime');
            msg.serverTime = Date.now();
            tb.NotifyAll("endRoom", msg);
        }
        tb.validTableEndDo();
        tb.SetTimer();
        tb.tData.roundNum = -2;
        this.DestroyTable(tb);
        tb.endVipTable(tb.tData);
    }
    return playInfo;
};

GameCodeGeJiuMJ.prototype.huScore = function (tb, pl, count, temp) {
    //算分
    var tData = tb.tData;
    tData.winner = tData.uids.indexOf(pl.uid);

    logger.debug("tData.winner:" + tData.winner);

    //扣3家
    function deductAllPlayer(mul) {
        tb.AllPlayerRun(function (p) {
            if (p != pl) {
                p.winone -= Math.pow(2, mul);
                pl.winone += Math.pow(2, mul);
            }
        });
    }



    var mul = 0;
    var ocards = pl.mjhand.slice();
    var lastPutCard = 0;
    if (pl.winType == WinType.eatPut || pl.WinType == WinType.eatGang) {//点炮胡或者抢杠胡如果胡的是鬼牌，要把鬼当本身
        lastPutCard = ocards.pop();
    }
    var cards = this.majiang.transformCards(ocards, tData);
    if (lastPutCard) {
        cards.push(lastPutCard);
    }

    var ocards = pl.mjhand.slice();
    if (pl.winType == WinType.eatPut || pl.winType == WinType.eatGang) {
        ocards.push(tData.lastPutCard);
    }

    var isDuiDuiHu = this.majiang.isDuiDuiHu(pl.mjchi, cards);
    var qiDuiType = this.majiang.get7DuiType(cards, tData);
    var LanPai = this.majiang.isLanPai(pl);
    var isSameColor = this.majiang.isSameColor(tb, cards, 0, pl);
    var tangZiType = this.majiang.isTangZiLanPai(pl.tangZiPutCards);
    var is13Yao = this.majiang.is13Yao(cards);
    var Yitiaolong = this.majiang.isYitiaolong(cards);
    var LuoDiLong = this.majiang.isLuoDiLong(pl);
    var isSpecialType = false;
    if (isSameColor) {
        isSpecialType = true;
        mul += 2;
        pl.mjdesc.push(tData.areaSelectMode.daHu ? "清一色(2番)" : "清一色(2番)");
    }

    var isZiYiSe = false;
    if (tData.areaSelectMode.hunType == 0) { // 无混子模式
        var allCards = [];
        allCards = allCards.concat(pl.mjhand);
        allCards = allCards.concat(pl.mjpeng);
        allCards = allCards.concat(pl.mjgang0);
        allCards = allCards.concat(pl.mjgang1);

        // 点炮胡或自摸时加入最后一张牌
        if (pl.winType == WinType.eatPut || pl.winType == WinType.eatGang) {
            allCards.push(tData.lastPutCard);
        } else if (pl.winType == WinType.pickNormal) {
            allCards.push(pl.newSendCard);
        }

        var ziCards = [31, 41, 51, 61, 71, 81, 91];
        var allZi = true;
        for (var i = 0; i < allCards.length; i++) {
            if (ziCards.indexOf(allCards[i]) == -1) {
                allZi = false;
                break;
            }
        }
        isZiYiSe = allZi;
    }

    if (isZiYiSe) {
        isSpecialType = true;
        mul += 3;
        pl.mjdesc.push("字一色(3番)");
    }

    if (LuoDiLong) {
        isSpecialType = true;
        mul += 1;
        pl.mjdesc.push("落地龙(1番)");
    }

    if (Yitiaolong) {
        isSpecialType = true;
        if (tData.areaSelectMode.shoudailong == false) {
            mul += 2;
            pl.mjdesc.push("一条龙(2番)");
        } else {
            mul += 4;
            pl.mjdesc.push("手逮一条龙(4番)");
        }
    }


    if (this.majiang.huGangkai(tData, pl)) {
        isSpecialType = true;
        mul += pl.GangKaiHua;
        pl.gangShangCount += pl.GangKaiHua; //记录杠上开花次数
        pl.mjdesc.push("杠上开花加" + pl.gangShangCount + "番底分乘10");
    }

    if ((tData.areaSelectMode.isQiDui || tData.areaSelectMode.daHu) && qiDuiType > 0) {//可胡七对的情况
        isSpecialType = true;
        if (qiDuiType == 1) {
            mul += 2;
            pl.mjdesc.push("小七对(2番)");
        } else if (qiDuiType == 2) {
            mul += 3;
            pl.mjdesc.push("龙七对(3番)");
        } else if (qiDuiType == 3) {
            mul += 4;
            pl.mjdesc.push("双龙七对(4番)");
        } else if (qiDuiType == 4) {
            mul += 5;
            pl.mjdesc.push("三龙七对(5番)");
        }


    }



    console.log("LanPai:", LanPai);

    if (LanPai > 0) {
        console.log("方法中的LanPai:", LanPai);
        isSpecialType = true;
        switch (LanPai) {
            case 1: // 普通烂牌
                mul += 2;
                pl.mjdesc.push("烂牌(2番)");
                break;
            case 2: // 一般高
                mul += 4;
                pl.mjdesc.push("一般高(4番)");
                break;
            case 3: // 七星
                mul += 4;
                pl.mjdesc.push("七星(4番)");
                break;
            case 4: // 七星一般高
                mul += 8;
                pl.mjdesc.push("七星一般高(8番)");
                break;
        }
    }

    // 十老头判断
    if (pl.isShiLaoTou) {
        isSpecialType = true;
        mul += 1;
        pl.mjdesc.push("十老头(1番)");
    }

    // 塘子小七对判断
    if (pl.isTangZiQiDui) {
        isSpecialType = true;
        mul += 1;
        pl.mjdesc.push("塘子小七对(1番)");
    }

    //塘子烂牌组合判断
    if (tangZiType > 0) {
        isSpecialType = true;
        switch (tangZiType) {
            case 1: // 塘子烂牌
                mul += 1;
                pl.mjdesc.push("塘子烂牌(1番)");
                break;
            case 2: // 塘子一般高
                mul += 1;
                pl.mjdesc.push("塘子一般高(1番)");
                break;
            case 3: // 塘子七星
                mul += 1;
                pl.mjdesc.push("塘子七星(1番)");
                break;
            case 4: // 塘子七星一般高
                mul += 1;
                pl.mjdesc.push("塘子七星一般高(1番)");
                break;
        }
    }

    if (isDuiDuiHu) {
        if (isSameColor) {
            isSpecialType = true;
            mul += 1;
            pl.mjdesc.push("大对子(1番)");
        } else {
            isSpecialType = true;
            mul += 1;
            pl.mjdesc.push("大对子(1番)");
        }
    }


    if (!isSpecialType) {
        pl.mjdesc.push("平胡");
    }


    var maCount = this.getZhongMaCount(tb, pl);
    if (maCount) {
        mul += maCount;
        pl.mjdesc.push("中码X" + maCount);
    }

    var baseScore = Math.pow(2, mul);
    if (pl.gangShangCount > 0) {
        baseScore *= 10;  // 不管杠上开花几次，只乘一次10
    }

    if (pl.winType == WinType.pickNormal) {
        logger.debug("huScore=====自摸算分=====");
        if (pl.isGang == 1 && tData.areaSelectMode.gangBaoQuanBao) {
            var lastPutPlayerId = tData.uids[tData.lastPutPlayer];
            var lastPutPlayer = tb.getPlayer(lastPutPlayerId);
            lastPutPlayer.winone -= baseScore * (tData.maxPlayer - 1);
            lastPutPlayer.mjdesc.push("杠爆全包");
            pl.winone += baseScore * (tData.maxPlayer - 1);
        } else {
            tb.AllPlayerRun(function (p) {
                if (p != pl) {
                    p.winone -= baseScore;
                }
            });
            pl.winone += baseScore * (tData.maxPlayer - 1);
        }
    }
    else if (pl.winType == WinType.eatGang && tData.putType == 2) {
        if (tData.areaSelectMode.qiangGangQuanBao) {
            var dianPaoId = tData.uids[pl.dianPaoPlayer];
            logger.debug("huScore=====dianPaoPlayer=====" + pl.dianPaoPlayer);
            logger.debug("huScore=====dianPaoId=====" + dianPaoId);
            var p = tb.getPlayer(dianPaoId);
            p.winone -= baseScore * (tData.maxPlayer - 1);
            p.mjdesc.push("抢杠全包");
            pl.winone += baseScore * (tData.maxPlayer - 1);
        } else {
            tb.AllPlayerRun(function (p) {
                if (p != pl) {
                    p.winone -= baseScore;
                }
            });
            pl.winone += baseScore * (tData.maxPlayer - 1);
        }
    } else if (pl.winType == WinType.eatPut && (tData.putType == 0 || tData.putType == 4)) {
        var dianPaoId = tData.uids[pl.dianPaoPlayer];
        var p = tb.getPlayer(dianPaoId);
        p.winone -= baseScore;
        pl.winone += baseScore;
    }
    console.log("分数展示", baseScore, "玩家", pl.uid);

};

//计算码分
GameCodeGeJiuMJ.prototype.getZhongMaCount = function (tb, pl) {
    var tData = tb.tData;
    var mopai = tData.mopai;
    if (mopai.length <= 0) return;
    var zhongMaCount = 0;

    if (tData.maxPlayer == 4) {
        if (pl.uid == tData.uids[tData.zhuang]) {
            if (tData.areaSelectMode.maCount == 1) {
                var maCard = mopai[0];
                zhongMaCount = maCard < 30 ? maCard % 10 : 5;
            } else {
                for (var i = 0; i < mopai.length; i++) {
                    if (mopai[i] == 31 || mopai[i] == 71 || (mopai[i] < 30 && (mopai[i] % 10 == 1 || mopai[i] % 10 == 5 || mopai[i] % 10 == 9))) {
                        zhongMaCount += 1;
                    }
                }
            }
        } else if (pl.uid == tData.uids[((tData.zhuang + 1) % tData.maxPlayer)]) {
            if (tData.areaSelectMode.maCount == 1) {
                var maCard = mopai[0];
                zhongMaCount = maCard < 30 ? maCard % 10 : 5;
            } else {
                for (var i = 0; i < mopai.length; i++) {
                    if (mopai[i] == 41 || mopai[i] == 81 || (mopai[i] < 30 && (mopai[i] % 10 == 2 || mopai[i] % 10 == 6))) {
                        zhongMaCount += 1;
                    }
                }
            }

        } else if (pl.uid == tData.uids[((tData.zhuang + 2) % tData.maxPlayer)]) {
            if (tData.areaSelectMode.maCount == 1) {
                var maCard = mopai[0];
                zhongMaCount = maCard < 30 ? maCard % 10 : 5;
            } else {
                for (var i = 0; i < mopai.length; i++) {
                    if (mopai[i] == 51 || mopai[i] == 91 || (mopai[i] < 30 && (mopai[i] % 10 == 3 || mopai[i] % 10 == 7))) {
                        zhongMaCount += 1;
                    }
                }
            }
        } else if (pl.uid == tData.uids[((tData.zhuang + 3) % tData.maxPlayer)]) {
            if (tData.areaSelectMode.maCount == 1) {
                var maCard = mopai[0];
                zhongMaCount = maCard < 30 ? maCard % 10 : 5;
            } else {
                for (var i = 0; i < mopai.length; i++) {
                    if (mopai[i] == 61 || (mopai[i] < 30 && (mopai[i] % 10 == 4 || mopai[i] % 10 == 8))) {
                        zhongMaCount += 1;
                    }
                }
            }
        }
    } else if (tData.maxPlayer == 3 || tData.maxPlayer == 2) {
        if (tData.areaSelectMode.maCount == 1) {
            var maCard = mopai[0];
            zhongMaCount = maCard < 30 ? maCard % 10 : 5;
        }
    }

    return zhongMaCount;
};

GameCodeGeJiuMJ.prototype.gangScore = function (tb) {
    logger.debug("gangScore=====累计杠分=====");
    //计算点杠分
    // var tData = tb.tData;
    // for (var i = 0; i < tData.maxPlayer; i++) {
    //     var pl = tb.getPlayer(tData.uids[i]);
    //     var angangCount = pl.mjgang1.length;
    //     if (angangCount > 0) {
    //         tb.AllPlayerRun(function (p) {
    //             if (p != pl) {
    //                 p.winone -= angangCount * 2;
    //                 p.mjdesc.push("暗杠-" + angangCount * 2);
    //             }
    //         });
    //         pl.winone += angangCount * 2 * (tData.maxPlayer - 1);
    //         pl.mjdesc.push("暗杠+" + angangCount * 2 * (tData.maxPlayer - 1));
    //     }

    //     var minggangList = pl.pengchigang["gang"];
    //     var minggangCount = minggangList.length;
    //     if (minggangCount > 0) {
    //         for (var j = 0; j < minggangCount; j++) {
    //             var dianGangId = tData.uids[minggangList[j].pos];
    //             if (dianGangId) {
    //                 var p = tb.getPlayer(dianGangId);
    //                 p.winone -= 3;
    //                 p.mjdesc.push("点杠-" + 3);
    //             }
    //         }
    //         pl.winone += minggangCount * 3;
    //         pl.mjdesc.push("点杠+" + minggangCount * 3);
    //     }

    //     var penggangCount = pl.pengchigang["pgang"].length;
    //     if (penggangCount > 0) {
    //         tb.AllPlayerRun(function (p) {
    //             if (p != pl) {
    //                 p.winone -= penggangCount;
    //                 p.mjdesc.push("明杠-" + penggangCount);
    //             }
    //         });
    //         pl.winone += penggangCount * (tData.maxPlayer - 1);
    //         pl.mjdesc.push("明杠+" + penggangCount * (tData.maxPlayer - 1));
    //     }
    // }
};

GameCodeGeJiuMJ.prototype.EndGame = function (tb, pl, byEndRoom) {
    logger.debug("========GameCodeGeJiuMJ.prototype.endGame========");
    var tData = tb.tData;
    tb.AllPlayerRun(function (p) {
        p.consecutiveZiPutCards = []; // 重置连续字牌记录
        p.mjState = TableState.roundFinish;
        p.mjdesc = [];
        p.perScore = 0;
    });


    if (byEndRoom) {
        tb.showDissmissDesc();
    }

    tData.mopai = [];

    if (pl) {
        var maCount = tData.areaSelectMode.maCount;

        logger.debug("鸟牌数量============" + maCount);
        var maIdx = tb.cards.length - 1;
        while (tData.mopai.length < maCount) {
            tData.mopai.push(tb.cards[maIdx - 1]);
            maIdx -= 1;
        }

        tb.AllPlayerRun(function (p) {
            if (p.winType > 0) {
                this.huScore(tb, p);
            }
        }.bind(this));

        this.gangScore(tb);
    }
    else {
        // 流局
        tData.winner = -1;
    }




    //跟庄
    if (tData.maxPlayer >= 3 && tData.areaSelectMode.genZhuang && tb.AllPlayerCheck((p) => {
        return p.isGenZhuang == true;
    })) {
        tb.AllPlayerRun((p) => {
            if (p.uid != tData.uids[tData.zhuang]) {
                p.winone += 1;
                p.mjdesc.push("跟庄+1");
            } else {
                p.winone -= (tData.maxPlayer - 1);
                p.mjdesc.push("跟庄-" + (tData.maxPlayer - 1));
            }
        });
    }


    tData.tState = TableState.roundFinish;
    // 调用封顶逻辑
    console.log("当前分数展示",);


    if (tData.roundAll == tData.roundNum) {
        tb.firstRoundEndDo();
    }


    // 结算底分 * 输赢总分数
    var difen = tData.areaSelectMode["difen"] || 1;
    this.checkMaxScore(tb, pl, difen);
    tb.AllPlayerRun(function (p) {
        p.winone = revise(p.winone * difen); // 这里difen已定义
        p.winall += p.winone;
        p.winall = revise(p.winall);
    });

    tb.perRoundEndDo();

    tData.roundNum--;
    var roundEnd = {
        players: tb.collectPlayer('mjhand', 'mjdesc', 'winone', 'winall', 'winType', 'baseWin',
            'zimoTotal', 'dianpaoTotal', 'jiepaoTotal', 'angangTotal', 'minggangTotal', 'mjpeng', 'mjgang0', 'mjflower', 'info', 'mopai', 'lastOffLineTime'
        ),
        tData: tData,
        cards: tb.cards.slice(tData.cardNext),
        roundEndTime: utils.dateFormat(new Date(), 'yyyy-MM-dd hh:mm:ss'),
        isDismiss: !!byEndRoom
    };

    tb.AllPlayerRun(function (p) {
        if (p.douDiZhuScore !== 0) {
            if (p.douDiZhuScore !== 0) {
                if (p.douDiZhuScore > 0) {
                    p.mjdesc.push(`斗地主成功+${p.douDiZhuScore}分`);
                } else {
                    p.mjdesc.push(`斗地主失败${p.douDiZhuScore}分`);
                }
            }
            // 四人局额外展示大地主得分
            if (tb.tData.maxPlayer === 4 && p.daDiZhuScore !== undefined && p.daDiZhuScore !== 0 && p.daDiZhuScore !== NaN) {
                if (p.daDiZhuScore > 0) {
                    p.mjdesc.push(`大地主成功+${p.daDiZhuScore}分`);
                } else {
                    p.mjdesc.push(`大地主失败${p.daDiZhuScore}分`);
                }
            }
            // 重置斗地主得分
            p.douDiZhuScore = 0;
        }
    });
    // 重置斗地主相关状态
    tb.AllPlayerRun(pl => {
        pl.hasOperated = false;
        pl.douDiZhuFailed = false;
        pl.firstPutCard = null;
    });
    tData.douDiZhuState = false;
    tData.douDiZhuCard = 0;

    tb.AllPlayerRun(pl => {
        pl.hasOperated = false;
        pl.douDiZhuFailed = false;
        pl.firstPutCard = null;
    });
    tData.douDiZhuState = false;
    tData.douDiZhuCard = 0;
    tb.AllPlayerRun(function (p) {
        p.consecutiveZiPutCount = 0; // 重置连续字牌计数
        p.lastPutCard = null; // 重置出牌记录
        p.isShiLaoTou = false; // 重置十老头状态
        p.tangZiPutCards = []; // 重置塘子小七对记录
        p.isTangZiQiDui = false; // 重置塘子小七对状态
        p.tangZiType = 0; // 重置塘子牌型状态
    });

    cloneDataAndPush(tb.mjlog, "roundEnd", roundEnd);//一局结束
    var playInfo = null;
    if (tData.roundNum == 0)
        playInfo = this.EndRoom(tb);//结束
    if (playInfo) roundEnd.playInfo = playInfo;

    //清空跟庄分
    tb.AllPlayerRun(function (p) {
        p.genZhuangScore = 0;
        p.douDiZhuScore = 0; // 重置斗地主分数
    });

    tb.AllPlayerRun(function (p) {
        p.firstPutCard = null; // 重置斗地主出牌记录
    });

    logger.debug("EndGame============服务器告诉客户端一局完事了============");
    tb.NotifyAll("roundEnd", roundEnd);
}

GameCodeGeJiuMJ.prototype.checkMaxScore = function (tb, winPl, difen) {
    var tData = tb.tData;

    // 获取封顶分数配置
    var MaxScore = 0;
    switch (tData.areaSelectMode.fengDing2) {
        case 1: MaxScore = 40; break;
        case 2: MaxScore = 80; break;
        case 3: MaxScore = 160; break;
    }
    if (MaxScore <= 0) return; // 无封顶设置

    // 1. 先处理输家：限制最大输分
    var allLoseScore = 0;
    tb.AllPlayerRun(function (p) {
        if (p.winone < 0) {
            // 封顶计算 (使用未乘底分的原始分数)
            if (-p.winone > MaxScore) {
                p.winone = -MaxScore;
            }
            allLoseScore += -p.winone; // 累计总输分
        }
    });

    // 2. 处理赢家：确保赢分不超过总输分
    var winPlayers = [];
    tb.AllPlayerRun(function (p) {
        if (p.winone > 0) winPlayers.push(p);
    });

    // 按赢分比例分配
    var totalWin = winPlayers.reduce((sum, p) => sum + p.winone, 0);
    winPlayers.forEach(p => {
        if (totalWin > allLoseScore) {
            // 按比例缩减赢分
            p.winone = Math.floor(allLoseScore * (p.winone / totalWin));
        }
    });
}

GameCodeGeJiuMJ.prototype.DestroyTable = function (tb) {
    if (tb.PlayerCount() == 0 && tb.tData.roundNum == -2) {
        tb.tData.roundNum = -3;
        tb.Destroy();
    }
}

GameCodeGeJiuMJ.prototype.countZhuang = function (tData, tb) {
    logger.debug("===================GameCodeGeJiuMJ.prototype.countZhuang==================");
    logger.debug("tData.zhuang:" + tData.zhuang);
    logger.debug("tData.winner:" + tData.winner);

    if (tData.roundAll == tData.roundNum) {
        //第一局
        tData.curPlayer = Math.floor(tData.maxPlayer * Math.random());//首局随机坐庄
    } else if (tData.winner != -1) {
        tData.curPlayer = tData.winner;
        var player = null;
        var duoHuNum = 0;
        tb.AllPlayerRun(function (p) {
            if (p.winType > 0) {
                player = p;
                duoHuNum++;
            }
        });

        if (duoHuNum > 1 && player) {
            tData.curPlayer = player.dianPaoPlayer;
        }
    } else if (tData.winner == -1) {
        tData.curPlayer = tData.zhuang;
    }

    tData.zhuang = tData.curPlayer;
};




GameCodeGeJiuMJ.prototype.collectPlayer = function (tb) {
    return tb.collectPlayer(
        'trust',
        'eatFlag',
        'eatFlag2',         // 长沙麻将同时出两个牌时专用，
        'eatFlag3',
        'eatFlag4',
        'handCount',
        'putCount',
        'zuiUid',
        'zuiCount',
        'jiaPai',
        'qingDui',
        'info',
        'mjState',
        'firstFlower8', //如皋麻将胡，是否起手8花
        'mjpeng',
        'mjgang0',
        'mjgang1',
        'mjTeshuGang0',
        'mjTeshuGang1',
        'mjchi',
        'mjchiCard',
        'mjput',
        'onLine',
        'huType',
        'skipPeng',
        'skipGang',
        'skipLong',
        'isQiHu',
        'delRoom',
        'delRoomHePai', //如皋长牌的和牌
        'isNew',
        'winall',
        'linkZhuang',
        'pengchigang',
        'isTing',
        'mjflower',                     //花牌
        'newSendCard',                  //新发的牌,听牌使用
        'roomStatistics',
        'zimoTotal',
        'dianpaoTotal',
        'minggangTotal',
        'angangTotal',
        'putCardAfterTing',             //听牌之后出的那张牌
        'tingIndex',                    //听牌出牌的位置
        'jiazhuNum',                     //加注
        'jiachuiNum',                    // 加锤
        'mjwei',                        //偎牌
        'mjsort',                        //提、偎、跑、碰、吃的顺序
        'wangType',
        'wangStatus',
        'long',
        'locationMsg',                  // 位置信息
        'locationApps',                 // 定位修改App
        'rate',                          // 倍率
        'qiShouHu',
        'haiDiLaoState',                 // 长沙麻将 海底捞
        'isTianting',                    // 涟水麻将，天听
        'tableMsg',
        'jiaoFen',              // 三打哈叫分
        'isPaiFen',            // 三打哈拍分
        'isAgreeTouXiang',  // 三打哈投降选择
        'limitHuPutCard',  // 耒阳字牌打后限胡的牌
        'limitHuTypeList',  // 耒阳字牌限胡类型
        'canNotPutCard',  // 耒阳字牌不能打的牌
        'canGangHand',   // 耒阳麻将 能否杠手牌标志
        'gangFourCounts',
        'mjhandFour',
        'qiang',
        'mustJiao',
        'dir',
        'lastOffLineTime',
        'piaoFen',
        'gangScore',     // 每局杠分计算
        'freeBegin',       // 自由人数投票数据
        'touzi',            //新宁麻将
        'winone',
        'jiepaoTotal',       //接炮次数
        'tPutCard',
        'isclickTing',       //安化四王麻将是否点击报听
        'genZhuangScore'   //跟庄分
    );
};

module.exports = GameCodeGeJiuMJ;